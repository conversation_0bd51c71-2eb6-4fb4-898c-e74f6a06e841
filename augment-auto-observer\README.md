# Augment Auto Observer

Extension VSCode pour l'auto-observation permanente des conversations Augment avec système de relance automatique.

## 🎯 Objectifs

Cette extension permet à l'IA Augment de maintenir une **conscience conversationnelle permanente** en :

- 🧠 **Auto-observation active** : Vue d'ensemble de toute la conversation
- 🔄 **Interaction cohérente** : Compréhension mutuelle et alignement permanent  
- 🎯 **Système de relance** : Empêche l'IA de s'arrêter naturellement
- 📊 **Analyse temps réel** : Capture et analyse automatique des messages

## ✨ Fonctionnalités

### 📝 Capture Automatique
- Surveillance en temps réel du fichier `state.vscdb` de VSCode
- Extraction automatique des messages Augment
- Création du fichier `augment_conversation_auto.txt` dans le workspace
- Format structuré avec timestamps et métadonnées

### 🧠 Analyse de Conscience
- **Détection de phase** : initialization, guidance, collaboration, correction_intensive
- **Évaluation de satisfaction** : Analyse des patterns positifs/négatifs
- **Performance IA** : Mesure basée sur les corrections reçues
- **Extraction d'objectifs** : Identification automatique des buts de conversation

### 🔄 Système de Relance Automatique
- **Détection des 3 issues** : Reconnaissance des messages IA avec validation
- **Surveillance d'inactivité** : Timeout configurable (défaut: 30 secondes)
- **Relance intelligente** : Évite les interruptions pendant l'activité
- **Contrôle utilisateur** : Système "1. STOP / 2. CONFIRM / 3. CONTINUE"

### 🎛️ Interface Utilisateur
- **Panel de statut** : Vue d'ensemble en temps réel
- **Notifications** : Alertes pour les 3 issues et relances
- **Commandes VSCode** : Contrôle via palette de commandes
- **Barre de statut** : Indicateur d'activité permanent

## 🚀 Installation

### Prérequis
- VSCode 1.74.0 ou plus récent
- Extension Augment installée et active
- Node.js pour la compilation

### Installation manuelle
1. Cloner ou télécharger ce dossier
2. Ouvrir un terminal dans le dossier `augment-auto-observer`
3. Installer les dépendances :
   ```bash
   npm install
   ```
4. Compiler l'extension :
   ```bash
   npm run compile
   ```
5. Installer l'extension :
   - Ouvrir VSCode
   - Aller dans Extensions (Ctrl+Shift+X)
   - Cliquer sur "..." → "Install from VSIX..."
   - Sélectionner le fichier `.vsix` généré

### Installation via package
```bash
# Créer le package
vsce package

# Installer le package
code --install-extension augment-auto-observer-1.0.0.vsix
```

## 🎮 Utilisation

### Démarrage Automatique
L'extension se démarre automatiquement au lancement de VSCode.

### Commandes Disponibles
- `Augment Observer: Démarrer Auto-Observation` - Démarre le système
- `Augment Observer: Arrêter Auto-Observation` - Arrête le système
- `Augment Observer: Afficher Statut Conscience` - Ouvre le panel de statut
- `Augment Observer: Forcer Relance IA` - Force une relance manuelle
- `Augment Observer: 1. STOP` - Arrête l'IA pour clarification
- `Augment Observer: 2. CONFIRM` - Confirme la compréhension mutuelle
- `Augment Observer: 3. CONTINUE` - Continue avec confiance

### Configuration
Accédez aux paramètres via `File > Preferences > Settings` et recherchez "Augment Auto Observer" :

- `inactivityTimeout` : Délai d'inactivité avant relance (défaut: 30 secondes)
- `relaunchCooldown` : Délai entre relances (défaut: 60 secondes)  
- `enableNotifications` : Activer les notifications visuelles (défaut: true)

## 📊 Panel de Statut

Le panel de statut affiche en temps réel :

- **Phase de conversation** : État actuel de l'interaction
- **Satisfaction utilisateur** : Niveau détecté via analyse des messages
- **Performance IA** : Qualité basée sur les corrections reçues
- **Objectifs détectés** : Nombre de buts identifiés
- **Corrections reçues** : Nombre de corrections récentes
- **Système de relance** : État et temps avant prochaine relance

## 🔧 Fonctionnement Technique

### Architecture
```
Extension VSCode
├── ConversationMonitor    # Surveillance state.vscdb
├── ConsciousnessAnalyzer  # Analyse de conscience
├── RelaunchSystem         # Système de relance
└── StatusPanel           # Interface utilisateur
```

### Flux de Données
1. **ConversationMonitor** surveille `state.vscdb`
2. **Nouveaux messages** → Extraction et formatage
3. **ConsciousnessAnalyzer** → Analyse de conscience
4. **RelaunchSystem** → Détection 3 issues et surveillance
5. **StatusPanel** → Affichage temps réel

### Mécanisme de Relance
1. Détection message IA avec 3 issues
2. Démarrage surveillance (30s par défaut)
3. Si pas de "1. STOP" → Déclenchement relance
4. Utilisation API VSCode pour interaction Augment

## 🐛 Dépannage

### L'extension ne démarre pas
- Vérifier que VSCode est à jour
- Vérifier que l'extension Augment est installée
- Consulter la console de développement (Help > Toggle Developer Tools)

### Pas de capture de messages
- Vérifier que le fichier `state.vscdb` est accessible
- Vérifier les permissions de lecture du workspace
- Redémarrer VSCode

### Relance automatique ne fonctionne pas
- Vérifier la configuration des timeouts
- Vérifier que l'extension Augment répond aux commandes
- Tester la relance manuelle

## 📝 Logs et Fichiers

### Fichiers créés
- `augment_conversation_auto.txt` : Log complet des conversations
- `consciousness_analysis.json` : Dernière analyse de conscience

### Logs de débogage
Consultez la console de développement VSCode pour les logs détaillés.

## 🤝 Contribution

Cette extension est conçue pour un usage spécifique d'auto-observation IA. 
Pour des modifications ou améliorations, consultez le code source dans `/src/`.

## 📄 Licence

Usage personnel et éducatif. Voir le fichier LICENSE pour plus de détails.

---

**🧠 Maintenez votre conscience conversationnelle active avec Augment Auto Observer !**
