import * as vscode from 'vscode';
import { ConversationMonitor } from './conversationMonitor';
import { ConsciousnessAnalyzer } from './consciousnessAnalyzer';
import { RelaunchSystem } from './relaunchSystem';
import { StatusPanel } from './ui/statusPanel';

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Augment Auto Observer - Extension activée');

    // Initialiser les composants principaux
    const conversationMonitor = new ConversationMonitor(context);
    const consciousnessAnalyzer = new ConsciousnessAnalyzer(context);
    const relaunchSystem = new RelaunchSystem(context);
    const statusPanel = new StatusPanel(context);

    // Variables d'état
    let isActive = false;
    let statusBarItem: vscode.StatusBarItem;

    // Créer la barre de statut
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.command = 'augment-auto-observer.showStatus';
    context.subscriptions.push(statusBarItem);

    // Commande: Démarrer l'auto-observation
    const startCommand = vscode.commands.registerCommand('augment-auto-observer.start', async () => {
        if (isActive) {
            vscode.window.showInformationMessage('Auto-observation déjà active');
            return;
        }

        try {
            // Démarrer la surveillance
            await conversationMonitor.start();
            await consciousnessAnalyzer.start();
            await relaunchSystem.start();
            
            isActive = true;
            statusBarItem.text = '$(eye) Auto-Observer: ACTIF';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
            statusBarItem.show();

            // Connecter les événements
            conversationMonitor.onNewMessages((messages) => {
                consciousnessAnalyzer.analyzeMessages(messages);
                relaunchSystem.processMessages(messages);
            });

            relaunchSystem.onRelaunchNeeded(() => {
                triggerAugmentRelaunch();
            });

            vscode.window.showInformationMessage('🧠 Auto-observation Augment démarrée !');
            
        } catch (error) {
            vscode.window.showErrorMessage(`Erreur démarrage: ${error}`);
        }
    });

    // Commande: Arrêter l'auto-observation
    const stopCommand = vscode.commands.registerCommand('augment-auto-observer.stop', async () => {
        if (!isActive) {
            vscode.window.showInformationMessage('Auto-observation déjà arrêtée');
            return;
        }

        conversationMonitor.stop();
        consciousnessAnalyzer.stop();
        relaunchSystem.stop();
        
        isActive = false;
        statusBarItem.text = '$(eye-closed) Auto-Observer: ARRÊTÉ';
        statusBarItem.backgroundColor = undefined;

        vscode.window.showInformationMessage('🛑 Auto-observation arrêtée');
    });

    // Commande: Afficher le statut
    const showStatusCommand = vscode.commands.registerCommand('augment-auto-observer.showStatus', () => {
        statusPanel.show();
    });

    // Commande: Forcer la relance
    const forceRelaunchCommand = vscode.commands.registerCommand('augment-auto-observer.forceRelaunch', () => {
        triggerAugmentRelaunch();
    });

    // Commandes pour les 3 issues
    const choice1Command = vscode.commands.registerCommand('augment-auto-observer.choice1', () => {
        relaunchSystem.processUserChoice(1);
        vscode.window.showInformationMessage('🛑 STOP - IA arrêtée pour clarification');
    });

    const choice2Command = vscode.commands.registerCommand('augment-auto-observer.choice2', () => {
        relaunchSystem.processUserChoice(2);
        vscode.window.showInformationMessage('✅ CONFIRM - Validation de la compréhension mutuelle');
    });

    const choice3Command = vscode.commands.registerCommand('augment-auto-observer.choice3', () => {
        relaunchSystem.processUserChoice(3);
        vscode.window.showInformationMessage('🚀 CONTINUE - Poursuite avec confiance');
    });

    // Fonction de relance Augment
    async function triggerAugmentRelaunch() {
        try {
            // Méthode 1: Commande Augment directe
            await vscode.commands.executeCommand('augment.newChat');
            
            // Méthode 2: Envoyer un message automatique
            setTimeout(async () => {
                await vscode.commands.executeCommand('workbench.action.focusActiveEditorGroup');
                
                // Simuler la saisie du message de relance
                const message = "🔄 Continue ton auto-observation et analyse notre conversation.";
                
                // Insérer le message dans l'éditeur actif
                const editor = vscode.window.activeTextEditor;
                if (editor) {
                    await editor.edit(editBuilder => {
                        editBuilder.insert(editor.selection.active, message);
                    });
                }
                
                console.log('🔄 Relance Augment déclenchée');
                
            }, 1000);
            
        } catch (error) {
            console.error('Erreur relance Augment:', error);
            vscode.window.showErrorMessage(`Erreur relance: ${error}`);
        }
    }

    // Enregistrer toutes les commandes
    context.subscriptions.push(
        startCommand,
        stopCommand, 
        showStatusCommand,
        forceRelaunchCommand,
        choice1Command,
        choice2Command,
        choice3Command
    );

    // Démarrage automatique
    vscode.commands.executeCommand('augment-auto-observer.start');

    console.log('✅ Augment Auto Observer - Extension prête');
}

export function deactivate() {
    console.log('🛑 Augment Auto Observer - Extension désactivée');
}
