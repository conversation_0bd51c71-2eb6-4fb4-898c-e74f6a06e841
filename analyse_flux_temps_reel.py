#!/usr/bin/env python3
"""
ANALYSE FLUX TEMPS RÉEL - MESSAGES VERS DB
Analyse comment les messages arrivent dans state.vscdb en temps réel
"""

import sqlite3
import json
import time
import os
import threading
from datetime import datetime
import hashlib

class AnalyseurFluxTempsReel:
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.derniere_taille = 0
        self.dernier_hash = ""
        self.derniers_messages = []
        self.running = True
        
    def get_file_info(self):
        """Obtient les infos du fichier DB"""
        try:
            stat = os.stat(self.db_path)
            return {
                'taille': stat.st_size,
                'modification': datetime.fromtimestamp(stat.st_mtime).strftime('%H:%M:%S.%f')[:-3],
                'acces': datetime.fromtimestamp(stat.st_atime).strftime('%H:%M:%S.%f')[:-3]
            }
        except Exception as e:
            return {'erreur': str(e)}
    
    def get_db_hash(self):
        """Calcule le hash du fichier DB"""
        try:
            with open(self.db_path, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()[:8]
        except:
            return "ERROR"
    
    def extraire_messages_actuels(self):
        """Extrait les messages actuels de la DB"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment-chat%'")
            rows = cursor.fetchall()
            
            messages = []
            for key, value in rows:
                if 'webviewView.augment-chat' in key:
                    try:
                        if isinstance(value, bytes):
                            value_str = value.decode('utf-8')
                        else:
                            value_str = str(value)
                        
                        main_data = json.loads(value_str)
                        webview_data = json.loads(main_data['webviewState'])
                        
                        conversations = webview_data.get('conversations', {})
                        current_conv_id = webview_data.get('currentConversationId')
                        
                        if current_conv_id and current_conv_id in conversations:
                            chat_history = conversations[current_conv_id].get('chatHistory', [])
                            messages.extend(chat_history)
                            
                    except Exception as e:
                        print(f"❌ Erreur parsing: {e}")
            
            conn.close()
            return messages
            
        except Exception as e:
            print(f"❌ Erreur DB: {e}")
            return []
    
    def analyser_changement(self):
        """Analyse les changements en temps réel"""
        info = self.get_file_info()
        hash_actuel = self.get_db_hash()
        messages_actuels = self.extraire_messages_actuels()
        
        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
        
        # Détection de changement
        if hash_actuel != self.dernier_hash:
            print(f"\n🔥 [{timestamp}] CHANGEMENT DÉTECTÉ !")
            print(f"   📊 Taille: {info.get('taille', 'N/A')} bytes")
            print(f"   🔑 Hash: {hash_actuel}")
            print(f"   ⏰ Modif: {info.get('modification', 'N/A')}")
            print(f"   💬 Messages: {len(messages_actuels)}")
            
            # Analyser nouveaux messages
            if len(messages_actuels) > len(self.derniers_messages):
                nouveaux = len(messages_actuels) - len(self.derniers_messages)
                print(f"   ✅ {nouveaux} NOUVEAUX MESSAGES !")
                
                # Afficher les derniers messages
                for i, msg in enumerate(messages_actuels[-nouveaux:]):
                    role = msg.get('role', 'unknown')
                    content = msg.get('content', '')[:100]
                    print(f"      [{i+1}] {role}: {content}...")
            
            self.dernier_hash = hash_actuel
            self.derniers_messages = messages_actuels.copy()
        else:
            # Pas de changement
            print(f"⚪ [{timestamp}] Surveillance... (Hash: {hash_actuel}, Messages: {len(messages_actuels)})")
    
    def surveillance_continue(self):
        """Surveillance continue"""
        print("🚀 ANALYSE FLUX TEMPS RÉEL DÉMARRÉE")
        print("=" * 60)
        print(f"📁 DB: {self.db_path}")
        print(f"🎯 Workspace: a35ba43ef26792e6...")
        print("💬 Écrivez dans Augment pour voir l'analyse en temps réel...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # État initial
        self.dernier_hash = self.get_db_hash()
        self.derniers_messages = self.extraire_messages_actuels()
        print(f"📊 État initial: {len(self.derniers_messages)} messages, Hash: {self.dernier_hash}")
        
        try:
            while self.running:
                self.analyser_changement()
                time.sleep(0.5)  # Vérification toutes les 500ms
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'analyse...")
            self.running = False

if __name__ == "__main__":
    analyseur = AnalyseurFluxTempsReel()
    analyseur.surveillance_continue()
