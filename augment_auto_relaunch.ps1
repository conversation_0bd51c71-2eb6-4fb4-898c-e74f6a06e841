# ========================================================================
# 🚀 AUGMENT AUTO RELAUNCH - SCRIPT POWERSHELL
# ========================================================================
# Script automatique pour relancer l'IA Augment après inactivité
# Surveille le fichier de log et envoie automatiquement des messages

param(
    [int]$InactivityTimeout = 30,  # D<PERSON>lai en secondes avant relance
    [string]$LogFile = "augment_conversation_auto.txt",
    [string]$RelaunchMessage = "Continue ton auto-observation"
)

Write-Host "🚀 AUGMENT AUTO RELAUNCH - DÉMARRAGE" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor Yellow
Write-Host "📊 Fichier surveillé: $LogFile" -ForegroundColor Cyan
Write-Host "⏰ Timeout inactivité: $InactivityTimeout secondes" -ForegroundColor Cyan
Write-Host "💬 Message de relance: $RelaunchMessage" -ForegroundColor Cyan
Write-Host "=" * 60 -ForegroundColor Yellow

# Vérifier que le fichier de log existe
if (-not (Test-Path $LogFile)) {
    Write-Host "❌ ERREUR: Fichier de log non trouvé: $LogFile" -ForegroundColor Red
    Write-Host "🐍 Démarrez d'abord le script Python augment_auto_logger_portable.py" -ForegroundColor Yellow
    exit 1
}

# Variables globales
$script:LastFileSize = (Get-Item $LogFile).Length
$script:LastAIMessageTime = Get-Date
$script:IsActive = $true

# Fonction pour détecter les nouveaux messages IA
function Test-NewAIMessage {
    try {
        $currentSize = (Get-Item $LogFile).Length
        
        if ($currentSize -gt $script:LastFileSize) {
            # Lire le nouveau contenu
            $newContent = Get-Content $LogFile -Tail 50 | Out-String
            
            # Chercher un message IA récent
            if ($newContent -match "🤖 ASSISTANT:" -and $newContent -match "\[(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})" ) {
                $script:LastAIMessageTime = Get-Date
                $script:LastFileSize = $currentSize
                Write-Host "🤖 Nouveau message IA détecté à $(Get-Date -Format 'HH:mm:ss')" -ForegroundColor Green
                return $true
            }
            
            $script:LastFileSize = $currentSize
        }
        
        return $false
    }
    catch {
        Write-Host "⚠️ Erreur lecture fichier: $_" -ForegroundColor Yellow
        return $false
    }
}

# Fonction pour calculer l'inactivité
function Get-InactivityDuration {
    return (Get-Date) - $script:LastAIMessageTime
}

# Fonction pour envoyer le message à Augment
function Send-MessageToAugment {
    param([string]$Message)
    
    try {
        Write-Host "📤 Envoi du message à Augment: '$Message'" -ForegroundColor Yellow
        
        # Méthode 1: Utiliser Add-Type pour automation Windows
        Add-Type -AssemblyName System.Windows.Forms
        Add-Type -AssemblyName System.Drawing
        
        # Trouver la fenêtre VSCode
        $vscodeProcess = Get-Process -Name "Code" -ErrorAction SilentlyContinue | Select-Object -First 1
        
        if ($vscodeProcess) {
            # Activer la fenêtre VSCode
            [System.Windows.Forms.SendKeys]::SendWait("%{TAB}")
            Start-Sleep -Milliseconds 500
            
            # Copier le message dans le presse-papier
            Set-Clipboard -Value $Message
            
            # Simuler Ctrl+V pour coller
            [System.Windows.Forms.SendKeys]::SendWait("^v")
            Start-Sleep -Milliseconds 300
            
            # Simuler Entrée pour envoyer
            [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
            
            Write-Host "✅ Message envoyé via automation Windows" -ForegroundColor Green
            return $true
        }
        else {
            Write-Host "⚠️ VSCode non trouvé, essai méthode alternative..." -ForegroundColor Yellow
            
            # Méthode 2: Utiliser WScript.Shell
            $shell = New-Object -ComObject WScript.Shell
            
            # Copier dans le presse-papier
            Set-Clipboard -Value $Message
            
            # Essayer d'activer VSCode
            $shell.AppActivate("Visual Studio Code")
            Start-Sleep -Milliseconds 500
            
            # Coller et envoyer
            $shell.SendKeys("^v")
            Start-Sleep -Milliseconds 300
            $shell.SendKeys("{ENTER}")
            
            Write-Host "✅ Message envoyé via WScript.Shell" -ForegroundColor Green
            return $true
        }
    }
    catch {
        Write-Host "❌ Erreur envoi message: $_" -ForegroundColor Red
        
        # Méthode de fallback: Notification
        try {
            # Créer une notification Windows
            [Windows.UI.Notifications.ToastNotificationManager, Windows.UI.Notifications, ContentType = WindowsRuntime] | Out-Null
            [Windows.Data.Xml.Dom.XmlDocument, Windows.Data.Xml.Dom.XmlDocument, ContentType = WindowsRuntime] | Out-Null
            
            $template = @"
<toast>
    <visual>
        <binding template="ToastText02">
            <text id="1">🔄 IA Inactive - Relance Nécessaire</text>
            <text id="2">Collez dans Augment: $Message</text>
        </binding>
    </visual>
</toast>
"@
            
            $xml = New-Object Windows.Data.Xml.Dom.XmlDocument
            $xml.LoadXml($template)
            
            $toast = [Windows.UI.Notifications.ToastNotification]::new($xml)
            $notifier = [Windows.UI.Notifications.ToastNotificationManager]::CreateToastNotifier("Augment Auto Relaunch")
            $notifier.Show($toast)
            
            Write-Host "🔔 Notification Windows envoyée" -ForegroundColor Cyan
        }
        catch {
            Write-Host "⚠️ Notification échouée, message copié dans presse-papier" -ForegroundColor Yellow
            Set-Clipboard -Value $Message
        }
        
        return $false
    }
}

# Fonction principale de surveillance
function Start-Monitoring {
    Write-Host "Demarrage de la surveillance..." -ForegroundColor Green

    # Initialiser avec l'etat actuel
    Test-NewAIMessage | Out-Null

    while ($script:IsActive) {
        try {
            # Verifier les nouveaux messages IA
            if (Test-NewAIMessage) {
                Write-Host "Timer d'inactivite reinitialise" -ForegroundColor Cyan
            }

            # Calculer la duree d'inactivite
            $inactivity = Get-InactivityDuration
            $inactivitySeconds = $inactivity.TotalSeconds

            # Afficher le statut toutes les 5 secondes
            if (($inactivitySeconds % 5) -lt 1) {
                Write-Host "Inactivite IA: $([math]::Round($inactivitySeconds))s / $InactivityTimeout s" -ForegroundColor Gray
            }

            # Verifier si relance necessaire
            if ($inactivitySeconds -ge $InactivityTimeout) {
                Write-Host "INACTIVITE DETECTEE: $([math]::Round($inactivitySeconds))s" -ForegroundColor Red
                Write-Host "Declenchement de la relance automatique..." -ForegroundColor Yellow

                if (Send-MessageToAugment -Message $RelaunchMessage) {
                    Write-Host "Relance reussie, attente de la reponse IA..." -ForegroundColor Green
                    # Reinitialiser le timer pour eviter les relances multiples
                    $script:LastAIMessageTime = Get-Date
                    Start-Sleep -Seconds 10  # Attendre 10 secondes avant de reprendre la surveillance
                }
                else {
                    Write-Host "Relance echouee, nouvelle tentative dans 30 secondes..." -ForegroundColor Yellow
                    Start-Sleep -Seconds 30
                }
            }

            # Attendre 1 seconde avant la prochaine verification
            Start-Sleep -Seconds 1
        }
        catch {
            Write-Host "Erreur dans la boucle de surveillance: $_" -ForegroundColor Red
            Start-Sleep -Seconds 5
        }
    }
}

# Gestionnaire d'arret propre
function Stop-Monitoring {
    Write-Host "Arret de la surveillance..." -ForegroundColor Yellow
    $script:IsActive = $false
}

# Capturer Ctrl+C pour arrêt propre
Register-EngineEvent -SourceIdentifier PowerShell.Exiting -Action {
    Stop-Monitoring
}

try {
    # Demarrer la surveillance
    Write-Host "Surveillance active - Appuyez sur Ctrl+C pour arreter" -ForegroundColor Green
    Write-Host ""

    Start-Monitoring
}
catch {
    Write-Host "ERREUR CRITIQUE: $_" -ForegroundColor Red
}
finally {
    Write-Host "Script termine" -ForegroundColor Yellow
}
