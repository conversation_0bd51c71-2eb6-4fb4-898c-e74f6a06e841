#!/usr/bin/env python3
"""
ANALYSEUR CORRÉLATION LONGUEUR ↔ DÉLAI
Mesure la corrélation entre :
- Longueur du message (caractères)
- <PERSON><PERSON><PERSON> d'enregistrement (ms)
- Type de message (user/assistant)
- Complexité du contenu (texte/code/JSON)
"""

import os
import time
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3
import re

class AnalyseurCorrelationLongueur(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.output_file = "correlation_longueur_delai.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.dernier_nombre_messages = 0
        self.numero_analyse = 1
        
        # DONNÉES DE CORRÉLATION
        self.correlations = []  # Liste des mesures
        self.derniere_taille_wal = 0
        
        self.init_output_file()
        self.init_wal_baseline()
        self.charger_etat_initial()
    
    def init_output_file(self):
        """Initialise le fichier d'analyse de corrélation"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("ANALYSEUR CORRÉLATION LONGUEUR ↔ DÉLAI\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write("🎯 OBJECTIF : Mesurer la corrélation longueur → délai\n")
            f.write("📊 VARIABLES MESURÉES :\n")
            f.write("   • Longueur message (caractères)\n")
            f.write("   • Délai d'enregistrement (ms)\n")
            f.write("   • Type de message (user/assistant)\n")
            f.write("   • Complexité contenu (texte/code/JSON)\n")
            f.write("   • Delta WAL (bytes)\n")
            f.write("   • Timestamp précis\n")
            f.write("📈 ANALYSE STATISTIQUE :\n")
            f.write("   • Corrélation longueur/délai\n")
            f.write("   • Moyenne par type de message\n")
            f.write("   • Outliers et anomalies\n")
            f.write("=" * 80 + "\n\n")
    
    def init_wal_baseline(self):
        """Établit la baseline WAL"""
        try:
            if os.path.exists(self.wal_path):
                self.derniere_taille_wal = os.path.getsize(self.wal_path)
                print(f"📊 WAL baseline: {self.derniere_taille_wal} bytes")
            else:
                self.derniere_taille_wal = 0
        except Exception as e:
            print(f"❌ Erreur WAL baseline: {e}")
            self.derniere_taille_wal = 0
    
    def charger_etat_initial(self):
        """Charge l'état initial"""
        try:
            messages_existants = self.extraire_messages()
            self.dernier_nombre_messages = len(messages_existants)
            print(f"📊 Messages baseline: {self.dernier_nombre_messages}")
        except Exception as e:
            print(f"❌ Erreur baseline: {e}")
            self.dernier_nombre_messages = 0
    
    def extraire_messages(self):
        """Extraction des messages avec contenu complet"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=1.0)
            conn.execute("PRAGMA journal_mode=WAL")
            cursor = conn.cursor()
            
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment-chat%'")
            rows = cursor.fetchall()
            
            for key, value in rows:
                if 'webviewView.augment-chat' in key:
                    try:
                        if isinstance(value, bytes):
                            value_str = value.decode('utf-8')
                        else:
                            value_str = str(value)
                        
                        main_data = json.loads(value_str)
                        webview_data = json.loads(main_data['webviewState'])
                        
                        conversations = webview_data.get('conversations', {})
                        current_conv_id = webview_data.get('currentConversationId')
                        
                        if current_conv_id and current_conv_id in conversations:
                            chat_history = conversations[current_conv_id].get('chatHistory', [])
                            conn.close()
                            return chat_history
                            
                    except Exception as e:
                        continue
            
            conn.close()
            return []
            
        except Exception as e:
            return []
    
    def analyser_complexite_contenu(self, content):
        """Analyse la complexité du contenu"""
        if not content:
            return "VIDE", 0
        
        content_str = str(content).strip()
        longueur = len(content_str)
        
        # Détection du type de contenu
        if re.search(r'[{}[\]"]', content_str) and ('json' in content_str.lower() or '{' in content_str):
            type_contenu = "JSON"
        elif re.search(r'(def |class |import |function|var |let |const )', content_str):
            type_contenu = "CODE"
        elif re.search(r'[<>]', content_str) and ('html' in content_str.lower() or '<' in content_str):
            type_contenu = "HTML"
        elif len(content_str.split('\n')) > 5:
            type_contenu = "MULTI-LIGNE"
        elif longueur > 200:
            type_contenu = "TEXTE-LONG"
        elif longueur > 50:
            type_contenu = "TEXTE-MOYEN"
        else:
            type_contenu = "TEXTE-COURT"
        
        return type_contenu, longueur
    
    def on_modified(self, event):
        """Détection WAL avec analyse de corrélation"""
        if event.is_directory:
            return
        
        if os.path.basename(event.src_path) == "state.vscdb-wal":
            timestamp_detection = datetime.now()
            
            # Analyser le delta WAL
            try:
                nouvelle_taille_wal = os.path.getsize(self.wal_path)
                delta_wal = nouvelle_taille_wal - self.derniere_taille_wal
                self.derniere_taille_wal = nouvelle_taille_wal
            except:
                delta_wal = 0
            
            print(f"⚡ [{timestamp_detection.strftime('%H:%M:%S.%f')[:-3]}] CORRÉLATION #{self.numero_analyse}")
            print(f"   📊 Delta WAL: +{delta_wal} bytes")
            
            # Lancer l'analyse de corrélation
            threading.Thread(target=self.mesurer_correlation, args=(timestamp_detection, delta_wal)).start()
    
    def mesurer_correlation(self, timestamp_detection, delta_wal):
        """Mesure la corrélation longueur ↔ délai"""
        
        # Mesures à intervalles optimisés (basés sur nos découvertes)
        intervalles_ms = [50, 100, 150, 200, 300, 500]
        
        for intervalle in intervalles_ms:
            time.sleep(intervalle / 1000.0)
            
            timestamp_mesure = datetime.now()
            delai_ms = (timestamp_mesure - timestamp_detection).total_seconds() * 1000
            
            # Extraire les messages
            messages_actuels = self.extraire_messages()
            nombre_actuel = len(messages_actuels)
            nouveaux_count = nombre_actuel - self.dernier_nombre_messages
            
            print(f"   📊 {delai_ms:3.0f}ms: {self.dernier_nombre_messages} → {nombre_actuel} (+{nouveaux_count})")
            
            if nouveaux_count > 0:
                # SUCCÈS ! Analyser la corrélation
                nouveaux_messages = messages_actuels[self.dernier_nombre_messages:]
                
                for i, message in enumerate(nouveaux_messages):
                    self.analyser_message_correlation(
                        timestamp_detection, delai_ms, delta_wal, message, i
                    )
                
                self.dernier_nombre_messages = nombre_actuel
                return
        
        # Aucun message détecté
        print(f"   ⚠️ Aucun nouveau message après 500ms")
    
    def analyser_message_correlation(self, timestamp_detection, delai_ms, delta_wal, message, index):
        """Analyse la corrélation pour un message spécifique"""
        
        # Extraire les données du message
        role = message.get('role', 'unknown')
        content = message.get('content', '')
        message_id = message.get('id', 'no-id')
        
        # Analyser le contenu
        type_contenu, longueur = self.analyser_complexite_contenu(content)
        
        # Créer l'entrée de corrélation
        correlation = {
            'numero': self.numero_analyse,
            'timestamp': timestamp_detection,
            'delai_ms': delai_ms,
            'role': role,
            'longueur': longueur,
            'type_contenu': type_contenu,
            'delta_wal': delta_wal,
            'message_id': message_id,
            'index': index,
            'content_preview': content[:100] if content else '[vide]'
        }
        
        # Ajouter à la liste des corrélations
        self.correlations.append(correlation)
        
        # Calculer les statistiques
        ratio_longueur_delai = longueur / delai_ms if delai_ms > 0 else 0
        
        # Affichage détaillé
        print(f"   🎯 MESSAGE #{self.numero_analyse} - CORRÉLATION ANALYSÉE")
        print(f"      • Role: {role}")
        print(f"      • Longueur: {longueur} caractères")
        print(f"      • Type: {type_contenu}")
        print(f"      • Délai: {delai_ms:.0f}ms")
        print(f"      • Ratio: {ratio_longueur_delai:.2f} chars/ms")
        print(f"      • Delta WAL: +{delta_wal} bytes")
        
        # Enregistrer la corrélation
        self.enregistrer_correlation(correlation)
        
        # Calculer les statistiques globales
        if len(self.correlations) >= 5:
            self.calculer_statistiques_globales()
        
        self.numero_analyse += 1
    
    def enregistrer_correlation(self, correlation):
        """Enregistre une corrélation dans le fichier"""
        with self.lock:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    timestamp_str = correlation['timestamp'].strftime('%H:%M:%S.%f')[:-3]
                    
                    f.write(f"[{timestamp_str}] CORRÉLATION #{correlation['numero']}\n")
                    f.write(f"Role: {correlation['role']} | Index: {correlation['index']}\n")
                    f.write(f"Longueur: {correlation['longueur']} chars | Type: {correlation['type_contenu']}\n")
                    f.write(f"Délai: {correlation['delai_ms']:.0f}ms | Delta WAL: +{correlation['delta_wal']} bytes\n")
                    f.write(f"Ratio: {correlation['longueur']/correlation['delai_ms']:.2f} chars/ms\n")
                    f.write("-" * 60 + "\n")
                    f.write(f"Aperçu: {correlation['content_preview']}\n")
                    f.write("=" * 80 + "\n\n")
                    f.flush()
                    
            except Exception as e:
                print(f"   ❌ Erreur enregistrement: {e}")
    
    def calculer_statistiques_globales(self):
        """Calcule les statistiques globales de corrélation"""
        if len(self.correlations) < 2:
            return
        
        # Séparer par type de message
        user_correlations = [c for c in self.correlations if c['role'] == 'user']
        assistant_correlations = [c for c in self.correlations if c['role'] == 'assistant']
        
        print(f"\n📈 STATISTIQUES GLOBALES ({len(self.correlations)} mesures):")
        
        if user_correlations:
            avg_delai_user = sum(c['delai_ms'] for c in user_correlations) / len(user_correlations)
            avg_longueur_user = sum(c['longueur'] for c in user_correlations) / len(user_correlations)
            print(f"   👤 UTILISATEUR: {avg_delai_user:.0f}ms avg, {avg_longueur_user:.0f} chars avg")
        
        if assistant_correlations:
            avg_delai_assistant = sum(c['delai_ms'] for c in assistant_correlations) / len(assistant_correlations)
            avg_longueur_assistant = sum(c['longueur'] for c in assistant_correlations) / len(assistant_correlations)
            print(f"   🤖 ASSISTANT: {avg_delai_assistant:.0f}ms avg, {avg_longueur_assistant:.0f} chars avg")
        
        # Corrélation globale
        delais = [c['delai_ms'] for c in self.correlations]
        longueurs = [c['longueur'] for c in self.correlations]
        
        if len(delais) > 1:
            # Calcul simple de corrélation
            avg_delai = sum(delais) / len(delais)
            avg_longueur = sum(longueurs) / len(longueurs)
            print(f"   📊 GLOBAL: {avg_delai:.0f}ms avg, {avg_longueur:.0f} chars avg")
    
    def demarrer_surveillance(self):
        """Démarre l'analyseur de corrélation"""
        print("🚀 ANALYSEUR CORRÉLATION LONGUEUR ↔ DÉLAI DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📄 Corrélations: {self.output_file}")
        print(f"🔢 Messages baseline: {self.dernier_nombre_messages}")
        print(f"📊 WAL baseline: {self.derniere_taille_wal} bytes")
        print("🎯 VARIABLES MESURÉES :")
        print("   • Longueur message (chars)")
        print("   • Délai enregistrement (ms)")
        print("   • Type contenu (texte/code/JSON)")
        print("   • Role (user/assistant)")
        print("💬 Écrivez des messages de différentes longueurs...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance filesystem
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'analyseur de corrélation...")
            self.calculer_statistiques_globales()
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    analyseur = AnalyseurCorrelationLongueur()
    analyseur.demarrer_surveillance()
