{"version": 3, "file": "conversationMonitor.js", "sourceRoot": "", "sources": ["../src/conversationMonitor.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,yBAAyB;AACzB,6BAA6B;AAC7B,qCAAqC;AAsBrC,MAAa,mBAAmB;IAS5B,YAAY,OAAgC;QAPpC,aAAQ,GAAG,KAAK,CAAC;QAIjB,qBAAgB,GAAG,CAAC,CAAC;QAIzB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;SACV;QAED,IAAI;YACA,iCAAiC;YACjC,IAAI,CAAC,SAAS,GAAG,MAAM,IAAI,CAAC,aAAa,EAAE,CAAC;YAC5C,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;aACrD;YAED,0BAA0B;YAC1B,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE;gBAClB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC7C;YAED,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,+BAA+B,CAAC,CAAC;YACtF,MAAM,IAAI,CAAC,WAAW,EAAE,CAAC;YAEzB,yBAAyB;YACzB,MAAM,IAAI,CAAC,eAAe,EAAE,CAAC;YAE7B,2BAA2B;YAC3B,IAAI,CAAC,aAAa,EAAE,CAAC;YAErB,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;YACrB,OAAO,CAAC,GAAG,CAAC,gCAAgC,CAAC,CAAC;SAEjD;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,yCAAyC,KAAK,EAAE,CAAC,CAAC;SACrE;IACL,CAAC;IAED,IAAI;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,IAAI,CAAC,OAAO,EAAE;YACd,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE,CAAC;YACrB,IAAI,CAAC,OAAO,GAAG,SAAS,CAAC;SAC5B;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;IACjD,CAAC;IAED,aAAa,CAAC,QAAmD;QAC7D,IAAI,CAAC,qBAAqB,GAAG,QAAQ,CAAC;IAC1C,CAAC;IAEO,KAAK,CAAC,aAAa;QACvB,MAAM,QAAQ,GAAG,IAAI,CAAC,IAAI,CACtB,OAAO,CAAC,GAAG,CAAC,OAAO,IAAI,EAAE,EACzB,4BAA4B,CAC/B,CAAC;QAEF,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,QAAQ,CAAC,EAAE;YAC1B,OAAO,SAAS,CAAC;SACpB;QAED,MAAM,aAAa,GAAG,EAAE,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAC/C,MAAM,UAAU,GAAyC,EAAE,CAAC;QAE5D,KAAK,MAAM,GAAG,IAAI,aAAa,EAAE;YAC7B,MAAM,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE,GAAG,EAAE,aAAa,CAAC,CAAC;YAC1D,IAAI,EAAE,CAAC,UAAU,CAAC,SAAS,CAAC,EAAE;gBAC1B,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC;gBACrC,UAAU,CAAC,IAAI,CAAC;oBACZ,IAAI,EAAE,SAAS;oBACf,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,OAAO,EAAE;iBAC/B,CAAC,CAAC;aACN;SACJ;QAED,IAAI,UAAU,CAAC,MAAM,KAAK,CAAC,EAAE;YACzB,OAAO,SAAS,CAAC;SACpB;QAED,0DAA0D;QAC1D,UAAU,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,CAAC,CAAC;QAE7C,gEAAgE;QAChE,KAAK,MAAM,EAAC,IAAI,EAAC,IAAI,UAAU,EAAE;YAC7B,IAAI,MAAM,IAAI,CAAC,uBAAuB,CAAC,IAAI,CAAC,EAAE;gBAC1C,OAAO,IAAI,CAAC;aACf;SACJ;QAED,OAAO,UAAU,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC;IAC9B,CAAC;IAEO,KAAK,CAAC,uBAAuB,CAAC,SAAiB;QACnD,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI;gBACA,8BAA8B;gBAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAY,CAAC;gBAE9C,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE;oBACtE,IAAI,GAAG,EAAE;wBACL,OAAO,CAAC,KAAK,CAAC,CAAC;wBACf,OAAO;qBACV;oBAED,EAAE,CAAC,GAAG,CACF,0EAA0E,EAC1E,CAAC,GAAG,EAAE,GAAG,EAAE,EAAE;wBACT,EAAE,CAAC,KAAK,EAAE,CAAC;wBACX,OAAO,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;oBACnB,CAAC,CACJ,CAAC;gBACN,CAAC,CAAC,CAAC;aACN;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,KAAK,CAAC,WAAW;QACrB,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAC3C,MAAM,MAAM,GAAG;WACZ,SAAS;aACP,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM;UACrD,IAAI,CAAC,SAAS;EACtB,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC;;CAEf,CAAC;QAEM,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,CAAC,CAAC;IACnD,CAAC;IAEO,KAAK,CAAC,eAAe;QACzB,IAAI;YACA,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC9D,IAAI,gBAAgB,EAAE;gBAClB,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,IAAI,EAAE,CAAC;gBAC3D,MAAM,aAAa,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;gBAE7D,IAAI,aAAa,IAAI,aAAa,CAAC,aAAa,CAAC,EAAE;oBAC/C,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC,WAAW,IAAI,EAAE,CAAC;oBACnE,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC;oBAC3C,OAAO,CAAC,GAAG,CAAC,oBAAoB,IAAI,CAAC,gBAAgB,WAAW,CAAC,CAAC;iBACrE;aACJ;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,sBAAsB,EAAE,KAAK,CAAC,CAAC;SAChD;IACL,CAAC;IAEO,aAAa;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO;QAE5B,IAAI,CAAC,OAAO,GAAG,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,EAAE;YAC1C,UAAU,EAAE,IAAI;YAChB,aAAa,EAAE,IAAI;SACtB,CAAC,CAAC;QAEH,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;YAC3B,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;QAEH,OAAO,CAAC,GAAG,CAAC,8BAA8B,IAAI,CAAC,SAAS,EAAE,CAAC,CAAC;IAChE,CAAC;IAEO,KAAK,CAAC,iBAAiB;QAC3B,IAAI;YACA,sDAAsD;YACtD,MAAM,IAAI,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;YAEvD,MAAM,gBAAgB,GAAG,MAAM,IAAI,CAAC,uBAAuB,EAAE,CAAC;YAC9D,IAAI,CAAC,gBAAgB;gBAAE,OAAO;YAE9B,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,IAAI,EAAE,CAAC;YAC3D,MAAM,aAAa,GAAG,gBAAgB,CAAC,qBAAqB,CAAC;YAE7D,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,CAAC,aAAa,CAAC;gBAAE,OAAO;YAE5D,MAAM,WAAW,GAAG,aAAa,CAAC,aAAa,CAAC,CAAC,WAAW,IAAI,EAAE,CAAC;YAEnE,IAAI,WAAW,CAAC,MAAM,GAAG,IAAI,CAAC,gBAAgB,EAAE;gBAC5C,MAAM,cAAc,GAAG,WAAW,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;gBAChE,MAAM,WAAW,GAA0B,EAAE,CAAC;gBAE9C,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,cAAc,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;oBAC5C,MAAM,OAAO,GAAG,cAAc,CAAC,CAAC,CAAC,CAAC;oBAClC,MAAM,YAAY,GAAG,IAAI,CAAC,aAAa,CAAC,OAAO,EAAE,IAAI,CAAC,gBAAgB,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;oBAChF,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;iBAClC;gBAED,qBAAqB;gBACrB,MAAM,IAAI,CAAC,WAAW,CAAC,WAAW,CAAC,CAAC;gBAEpC,iCAAiC;gBACjC,IAAI,IAAI,CAAC,qBAAqB,EAAE;oBAC5B,IAAI,CAAC,qBAAqB,CAAC,WAAW,CAAC,CAAC;iBAC3C;gBAED,IAAI,CAAC,gBAAgB,GAAG,WAAW,CAAC,MAAM,CAAC;gBAC3C,OAAO,CAAC,GAAG,CAAC,KAAK,WAAW,CAAC,MAAM,4BAA4B,CAAC,CAAC;aACpE;SAEJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,+BAA+B,EAAE,KAAK,CAAC,CAAC;SACzD;IACL,CAAC;IAEO,KAAK,CAAC,uBAAuB;QACjC,IAAI,CAAC,IAAI,CAAC,SAAS;YAAE,OAAO,IAAI,CAAC;QAEjC,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,EAAE;YAC3B,IAAI;gBACA,8BAA8B;gBAC9B,MAAM,OAAO,GAAG,OAAO,CAAC,SAAS,CAAY,CAAC;gBAE9C,MAAM,EAAE,GAAG,IAAI,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,SAAU,EAAE,OAAO,CAAC,aAAa,EAAE,CAAC,GAAG,EAAE,EAAE;oBAC5E,IAAI,GAAG,EAAE;wBACL,OAAO,CAAC,IAAI,CAAC,CAAC;wBACd,OAAO;qBACV;oBAED,EAAE,CAAC,GAAG,CACF,4EAA4E,EAC5E,CAAC,GAAG,EAAE,GAAQ,EAAE,EAAE;wBACd,EAAE,CAAC,KAAK,EAAE,CAAC;wBAEX,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE;4BACb,OAAO,CAAC,IAAI,CAAC,CAAC;4BACd,OAAO;yBACV;wBAED,IAAI;4BACA,MAAM,KAAK,GAAG,OAAO,GAAG,CAAC,KAAK,KAAK,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,KAAK,CAAC,QAAQ,EAAE,CAAC;4BAC/E,MAAM,QAAQ,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC;4BACnC,MAAM,WAAW,GAAG,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,YAAY,CAAC,CAAC;4BACtD,OAAO,CAAC,WAAW,CAAC,CAAC;yBACxB;wBAAC,OAAO,UAAU,EAAE;4BACjB,OAAO,CAAC,IAAI,CAAC,CAAC;yBACjB;oBACL,CAAC,CACJ,CAAC;gBACN,CAAC,CAAC,CAAC;aACN;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,IAAI,CAAC,CAAC;aACjB;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,aAAa,CAAC,WAAgB,EAAE,KAAa;QACjD,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAE3C,+BAA+B;QAC/B,MAAM,WAAW,GAAG,WAAW,CAAC,eAAe,IAAI,EAAE,CAAC;QAEtD,6BAA6B;QAC7B,IAAI,gBAAgB,GAAG,EAAE,CAAC;QAC1B,MAAM,SAAS,GAAa,EAAE,CAAC;QAE/B,IAAI,WAAW,CAAC,uBAAuB,EAAE;YACrC,KAAK,MAAM,IAAI,IAAI,WAAW,CAAC,uBAAuB,EAAE;gBACpD,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,OAAO,EAAE;oBACjC,gBAAgB,GAAG,IAAI,CAAC,OAAO,CAAC;iBACnC;qBAAM,IAAI,IAAI,CAAC,IAAI,KAAK,CAAC,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACzC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,SAAS,CAAC,CAAC;iBACxD;aACJ;SACJ;QAED,OAAO;YACH,KAAK;YACL,SAAS;YACT,WAAW,EAAE,WAAW;YACxB,iBAAiB,EAAE,gBAAgB;YACnC,SAAS;YACT,SAAS,EAAE,WAAW,CAAC,UAAU,IAAI,EAAE;SAC1C,CAAC;IACN,CAAC;IAEO,KAAK,CAAC,WAAW,CAAC,QAA+B;QACrD,IAAI,CAAC,IAAI,CAAC,OAAO;YAAE,OAAO;QAE1B,IAAI,UAAU,GAAG,EAAE,CAAC;QAEpB,KAAK,MAAM,GAAG,IAAI,QAAQ,EAAE;YACxB,UAAU,IAAI,IAAI,GAAG,CAAC,SAAS,cAAc,GAAG,CAAC,KAAK,IAAI,CAAC;YAC3D,UAAU,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC;YAEpC,IAAI,GAAG,CAAC,WAAW,EAAE;gBACjB,UAAU,IAAI,oBAAoB,GAAG,CAAC,WAAW,MAAM,CAAC;aAC3D;YAED,IAAI,GAAG,CAAC,iBAAiB,EAAE;gBACvB,UAAU,IAAI,kBAAkB,GAAG,CAAC,iBAAiB,MAAM,CAAC;aAC/D;YAED,IAAI,GAAG,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE;gBAC1B,UAAU,IAAI,cAAc,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,MAAM,CAAC;aAC9D;YAED,UAAU,IAAI,GAAG,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC;SACzC;QAED,EAAE,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,MAAM,CAAC,CAAC;IACxD,CAAC;CACJ;AAhUD,kDAgUC"}