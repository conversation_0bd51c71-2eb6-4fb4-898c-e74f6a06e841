#!/usr/bin/env python3
"""
AUGMENT AUTO LOGGER PORTABLE - SCRIPT UNIVERSEL
===============================================

Script portable pour logging automatique des conversations Augment.
Fonctionne dans n'importe quel projet, détecte automatiquement le workspace.

UTILISATION:
1. Copiez ce fichier dans votre nouveau projet
2. Exécutez: python augment_auto_logger_portable.py
3. Le fichier 'augment_conversation_auto.txt' sera créé et mis à jour en temps réel

COMPORTEMENT:
- Détection automatique du workspace VSCode actuel
- Création du fichier de log dans le répertoire du projet
- Écriture en temps réel de toutes les conversations Augment
- Aucune configuration requise
"""

import sqlite3
import json
import datetime
import time
import threading
from pathlib import Path
import os
import sys
import hashlib
import re
from collections import deque
from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any

# Essayer d'importer watchdog
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    print("⚠️ Module watchdog requis. Installation: pip install watchdog")

class PortableConversationWatcher(FileSystemEventHandler):
    def __init__(self, logger_instance):
        self.logger = logger_instance
        super().__init__()

    def on_modified(self, event):
        if event.is_directory:
            return

        # OPTIMISATION : Surveiller le fichier WAL pour détection ultra-rapide
        if event.src_path.endswith('state.vscdb-wal') or event.src_path.endswith('state.vscdb'):
            self.logger.process_file_change_optimized()

class PortableAugmentLogger:
    def __init__(self):
        # Détection automatique du répertoire de projet
        self.project_dir = self.detect_project_directory()

        # Détection automatique du fichier state.vscdb
        self.state_file = self.find_state_file()

        # Fichiers de log dans le projet actuel
        self.log_file = self.project_dir / "augment_conversation_auto.txt"
        self.json_log = self.project_dir / "augment_conversation_auto.json"

        self.last_message_count = 0
        self.last_conversation_id = None
        self.observer = None
        self.running = False

        # OPTIMISATIONS AVANCÉES
        self.last_hash = ""
        self.last_file_size = 0
        self.wal_path = str(self.state_file) + "-wal" if self.state_file else None

        # ANALYSEUR TEMPS RÉEL INTÉGRÉ
        self.analyse_temps_reel = True
        self.derniere_taille_fichier = 0
        self.dernier_nombre_lignes = 0
        self.lock_analyse = threading.Lock()

        # SYSTÈME D'AUTO-OBSERVATION AVANCÉ
        self.conversation_consciousness = ConversationConsciousness()
        self.feedback_system = ConversationFeedbackSystem()
        self.validation_system = ThreeWayValidationSystem()
        self.auto_relaunch_system = AutoRelaunchSystem()

        # ÉTAT DE CONSCIENCE CONVERSATIONNELLE
        self.consciousness_active = True
        self.last_analysis_time = time.time()
        self.conversation_history = deque(maxlen=1000)  # Garde les 1000 derniers messages
        
        print(f"🎯 AUGMENT AUTO LOGGER PORTABLE")
        print(f"=" * 50)
        print(f"📁 Projet détecté: {self.project_dir}")
        print(f"📄 State file: {self.state_file}")
        print(f"📝 Log file: {self.log_file}")
        
        # Initialiser les fichiers
        self.init_log_files()

        # Obtenir l'état initial
        self.get_initial_state()

        # Initialiser l'analyseur temps réel
        self.init_analyseur_temps_reel()
    
    def detect_project_directory(self):
        """Détecte automatiquement le répertoire de projet"""
        # Méthode 1: Répertoire du script
        script_dir = Path(__file__).parent.absolute()
        
        # Méthode 2: Répertoire de travail actuel
        current_dir = Path.cwd()
        
        # Préférer le répertoire du script s'il semble être un projet
        if self.looks_like_project(script_dir):
            return script_dir
        elif self.looks_like_project(current_dir):
            return current_dir
        else:
            # Par défaut, utiliser le répertoire du script
            return script_dir
    
    def looks_like_project(self, directory):
        """Vérifie si un répertoire ressemble à un projet"""
        project_indicators = [
            '.git', '.vscode', 'package.json', 'requirements.txt', 
            'Cargo.toml', 'go.mod', 'pom.xml', 'build.gradle'
        ]
        
        for indicator in project_indicators:
            if (directory / indicator).exists():
                return True
        return False
    
    def find_state_file(self):
        """Trouve automatiquement le fichier state.vscdb du workspace actuel"""
        base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
        
        if not base_path.exists():
            print(f"⚠️ Répertoire workspaceStorage non trouvé: {base_path}")
            return None
        
        # Chercher tous les fichiers state.vscdb
        state_files = []
        for workspace_dir in base_path.iterdir():
            if workspace_dir.is_dir():
                state_file = workspace_dir / "state.vscdb"
                if state_file.exists():
                    # Vérifier la date de modification (plus récent = plus probable)
                    mtime = state_file.stat().st_mtime
                    state_files.append((state_file, mtime))
        
        if not state_files:
            print(f"⚠️ Aucun fichier state.vscdb trouvé")
            return None
        
        # Trier par date de modification (plus récent en premier)
        state_files.sort(key=lambda x: x[1], reverse=True)
        
        # Prendre le plus récent qui contient des conversations Augment
        for state_file, _ in state_files:
            if self.has_augment_conversations(state_file):
                return state_file
        
        # Si aucun n'a de conversations Augment, prendre le plus récent
        return state_files[0][0]
    
    def has_augment_conversations(self, state_file):
        """Vérifie si un fichier state.vscdb contient des conversations Augment"""
        try:
            conn = sqlite3.connect(str(state_file))
            cursor = conn.cursor()
            cursor.execute("SELECT key FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except Exception:
            return False
    
    def init_log_files(self):
        """Initialise les fichiers de log"""
        timestamp = datetime.datetime.now().isoformat()
        
        # Fichier texte
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write("AUGMENT CONVERSATION - LOG AUTOMATIQUE\n")
            f.write(f"Démarré: {timestamp}\n")
            f.write(f"Projet: {self.project_dir}\n")
            f.write(f"Source: {self.state_file}\n")
            f.write("=" * 80 + "\n\n")
        
        # Fichier JSON
        initial_data = {
            "started_at": timestamp,
            "project_directory": str(self.project_dir),
            "source_file": str(self.state_file),
            "conversation_id": None,
            "messages": [],
            "total_messages": 0
        }
        with open(self.json_log, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2, ensure_ascii=False)
    
    def get_initial_state(self):
        """Obtient l'état initial de la conversation"""
        try:
            conversation_data = self.extract_conversation_data()
            if conversation_data:
                conversations = conversation_data.get('conversations', {})
                current_conv_id = conversation_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    self.last_message_count = len(chat_history)
                    self.last_conversation_id = current_conv_id
                    
                    print(f"📊 État initial: {self.last_message_count} messages")
        except Exception as e:
            print(f"⚠️ Erreur état initial: {e}")
    
    def extract_conversation_data(self):
        """Extrait les données de conversation - VERSION OPTIMISÉE"""
        try:
            if not self.state_file or not self.state_file.exists():
                return None

            # OPTIMISATION : Connexion SQLite optimisée avec timeout et mode WAL
            conn = sqlite3.connect(str(self.state_file), timeout=1.0)
            conn.execute("PRAGMA journal_mode=WAL")
            cursor = conn.cursor()

            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat';")
            result = cursor.fetchone()

            if not result:
                conn.close()
                return None

            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)

            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])

            conn.close()
            return webview_data

        except Exception as e:
            return None
    
    def format_message(self, message_data, index):
        """Formate un message pour le log"""
        timestamp = datetime.datetime.now().isoformat()
        
        # Extraire contenu utilisateur
        user_content = message_data.get('request_message', '')
        
        # Extraire réponse assistant
        assistant_content = ""
        tools_used = []
        
        if 'structured_output_nodes' in message_data:
            for node in message_data['structured_output_nodes']:
                if node.get('type') == 0 and node.get('content'):
                    assistant_content = node['content']
                elif node.get('type') == 5 and node.get('tool_use'):
                    tool_info = node['tool_use']
                    tools_used.append(tool_info.get('tool_name', 'unknown'))
        
        return {
            'index': index,
            'timestamp': timestamp,
            'user_message': user_content,
            'assistant_response': assistant_content,
            'tools_used': tools_used,
            'request_id': message_data.get('request_id', ''),
            'message_timestamp': message_data.get('timestamp', '')
        }
    
    def append_new_messages_optimized(self, new_messages):
        """Ajoute les nouveaux messages aux logs - VERSION OPTIMISÉE"""
        if not new_messages:
            return

        # Écriture immédiate avec gestion des verrous
        for attempt in range(5):
            try:
                # Log texte - écriture immédiate
                with open(self.log_file, 'a', encoding='utf-8') as f:
                    for msg in new_messages:
                        f.write(f"[{msg['timestamp']}] MESSAGE #{msg['index']}\n")
                        f.write("-" * 60 + "\n")

                        if msg['user_message']:
                            f.write(f"👤 UTILISATEUR:\n{msg['user_message']}\n\n")

                        if msg['assistant_response']:
                            f.write(f"🤖 ASSISTANT:\n{msg['assistant_response']}\n\n")

                        if msg['tools_used']:
                            f.write(f"🔧 OUTILS: {', '.join(msg['tools_used'])}\n\n")

                        f.write("=" * 80 + "\n\n")

                    # Forcer l'écriture immédiate
                    f.flush()
                    os.fsync(f.fileno())
                break  # Succès

            except Exception as e:
                if attempt < 4:
                    # Fichier alternatif en cas de verrou
                    alt_file = self.log_file.with_suffix('.alt.txt')
                    try:
                        with open(alt_file, 'a', encoding='utf-8') as f:
                            for msg in new_messages:
                                f.write(f"[{msg['timestamp']}] MESSAGE #{msg['index']} (ALT)\n")
                                f.write("-" * 60 + "\n")
                                if msg['user_message']:
                                    f.write(f"👤 UTILISATEUR:\n{msg['user_message']}\n\n")
                                if msg['assistant_response']:
                                    f.write(f"🤖 ASSISTANT:\n{msg['assistant_response']}\n\n")
                                f.write("=" * 80 + "\n\n")
                            f.flush()
                            os.fsync(f.fileno())
                        break
                    except:
                        time.sleep(0.05)
                        continue

        # Log JSON - mise à jour rapide
        try:
            with open(self.json_log, 'r', encoding='utf-8') as f:
                data = json.load(f)

            data['messages'].extend(new_messages)
            data['last_update'] = datetime.datetime.now().isoformat()
            data['total_messages'] = len(data['messages'])
            data['conversation_id'] = self.last_conversation_id

            with open(self.json_log, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            pass

        print(f"✅ {len(new_messages)} nouveaux messages loggés automatiquement")

    def append_new_messages(self, new_messages):
        """Ancienne méthode - redirige vers la version optimisée"""
        return self.append_new_messages_optimized(new_messages)

    def get_file_hash(self):
        """Calcule le hash du fichier pour détecter les vrais changements"""
        try:
            if not self.state_file or not self.state_file.exists():
                return ""

            with open(self.state_file, 'rb') as f:
                content = f.read()
                return hashlib.md5(content).hexdigest()[:8]
        except:
            return ""

    def get_file_size(self):
        """Obtient la taille du fichier"""
        try:
            if not self.state_file or not self.state_file.exists():
                return 0
            return self.state_file.stat().st_size
        except:
            return 0

    def has_real_change(self):
        """Vérifie s'il y a un vrai changement (hash + taille)"""
        current_hash = self.get_file_hash()
        current_size = self.get_file_size()

        if current_hash != self.last_hash or current_size != self.last_file_size:
            self.last_hash = current_hash
            self.last_file_size = current_size
            return True
        return False
    
    def process_file_change(self):
        """Traite un changement de fichier détecté - VERSION OPTIMISÉE"""
        try:
            # Tentatives multiples pour éviter les verrous
            for attempt in range(5):
                try:
                    conversation_data = self.extract_conversation_data()

                    if conversation_data:
                        conversations = conversation_data.get('conversations', {})
                        current_conv_id = conversation_data.get('currentConversationId')

                        if current_conv_id and current_conv_id in conversations:
                            chat_history = conversations[current_conv_id].get('chatHistory', [])

                            # Vérifier s'il y a de nouveaux messages
                            if len(chat_history) > self.last_message_count:
                                new_messages_raw = chat_history[self.last_message_count:]
                                new_messages = []

                                for i, msg_data in enumerate(new_messages_raw):
                                    formatted_msg = self.format_message(
                                        msg_data,
                                        self.last_message_count + i + 1
                                    )
                                    new_messages.append(formatted_msg)

                                # Écriture immédiate avec gestion des verrous
                                self.append_new_messages_optimized(new_messages)

                                self.last_message_count = len(chat_history)
                                self.last_conversation_id = current_conv_id
                    break  # Succès, sortir de la boucle

                except Exception as e:
                    if attempt < 4:  # Réessayer
                        time.sleep(0.05)  # Pause très courte
                        continue
                    else:
                        print(f"⚠️ Erreur après 5 tentatives: {e}")

        except Exception as e:
            print(f"⚠️ Erreur traitement changement: {e}")

    def process_file_change_optimized(self):
        """Version ultra-optimisée avec détection WAL et hash"""
        try:
            # OPTIMISATION 1 : Vérifier s'il y a un vrai changement
            if not self.has_real_change():
                return  # Pas de changement réel, ignorer

            # OPTIMISATION 2 : Mesures temporelles précises (basées sur dossier 3)
            intervalles_ms = [20, 50, 100, 200, 300]  # Intervalles optimisés

            for intervalle in intervalles_ms:
                time.sleep(intervalle / 1000.0)

                # OPTIMISATION 3 : Tentatives multiples avec timeout court
                for attempt in range(3):
                    try:
                        conversation_data = self.extract_conversation_data()

                        if conversation_data:
                            conversations = conversation_data.get('conversations', {})
                            current_conv_id = conversation_data.get('currentConversationId')

                            if current_conv_id and current_conv_id in conversations:
                                chat_history = conversations[current_conv_id].get('chatHistory', [])

                                # Vérifier s'il y a de nouveaux messages
                                if len(chat_history) > self.last_message_count:
                                    new_messages_raw = chat_history[self.last_message_count:]
                                    new_messages = []

                                    for i, msg_data in enumerate(new_messages_raw):
                                        formatted_msg = self.format_message(
                                            msg_data,
                                            self.last_message_count + i + 1
                                        )
                                        new_messages.append(formatted_msg)

                                    # Écriture immédiate optimisée
                                    self.append_new_messages_optimized(new_messages)

                                    self.last_message_count = len(chat_history)
                                    self.last_conversation_id = current_conv_id

                                    print(f"⚡ Capture ultra-rapide: {len(new_messages)} messages en {intervalle}ms")

                                    # ANALYSE TEMPS RÉEL IMMÉDIATE
                                    if self.analyse_temps_reel:
                                        threading.Thread(target=self.analyser_fichier_temps_reel).start()

                                    # ANALYSE DE CONSCIENCE CONVERSATIONNELLE
                                    if self.consciousness_active:
                                        threading.Thread(target=self.process_consciousness_analysis).start()

                                    # ANALYSE DES MESSAGES POUR SYSTÈME DE RELANCE
                                    self.analyze_messages_for_relaunch(new_messages)

                                    return  # Succès, sortir
                        break  # Succès de l'extraction, passer à l'intervalle suivant

                    except Exception as e:
                        if attempt < 2:  # Réessayer
                            time.sleep(0.01)  # Pause très courte
                            continue
                        else:
                            break  # Échec, passer à l'intervalle suivant

        except Exception as e:
            print(f"⚠️ Erreur traitement optimisé: {e}")

    def init_analyseur_temps_reel(self):
        """Initialise l'analyseur temps réel intégré"""
        try:
            if self.log_file.exists():
                stat = self.log_file.stat()
                self.derniere_taille_fichier = stat.st_size

                with open(self.log_file, 'r', encoding='utf-8') as f:
                    lignes = f.readlines()
                    self.dernier_nombre_lignes = len(lignes)

                print(f"📊 Analyseur temps réel: {self.derniere_taille_fichier} bytes, {self.dernier_nombre_lignes} lignes")
            else:
                self.derniere_taille_fichier = 0
                self.dernier_nombre_lignes = 0

        except Exception as e:
            print(f"⚠️ Erreur init analyseur: {e}")
            self.derniere_taille_fichier = 0
            self.dernier_nombre_lignes = 0

    def analyser_fichier_temps_reel(self):
        """Analyse le fichier de log en temps réel"""
        with self.lock_analyse:
            try:
                if not self.log_file.exists():
                    return

                # Obtenir les nouvelles informations
                stat = self.log_file.stat()
                nouvelle_taille = stat.st_size

                if nouvelle_taille <= self.derniere_taille_fichier:
                    return  # Pas de changement

                with open(self.log_file, 'r', encoding='utf-8') as f:
                    lignes = f.readlines()
                    nouveau_nombre_lignes = len(lignes)

                # Analyser les changements
                delta_taille = nouvelle_taille - self.derniere_taille_fichier
                delta_lignes = nouveau_nombre_lignes - self.dernier_nombre_lignes

                if delta_lignes > 0:
                    timestamp = datetime.datetime.now().strftime('%H:%M:%S.%f')[:-3]
                    print(f"\n🔥 [{timestamp}] ANALYSE TEMPS RÉEL - NOUVEAUX CONTENUS DÉTECTÉS !")
                    print(f"   📊 Taille: +{delta_taille} bytes | Lignes: +{delta_lignes}")

                    # Afficher les nouvelles lignes
                    nouvelles_lignes = lignes[self.dernier_nombre_lignes:]
                    self.analyser_nouvelles_lignes(nouvelles_lignes)

                # Mettre à jour l'état
                self.derniere_taille_fichier = nouvelle_taille
                self.dernier_nombre_lignes = nouveau_nombre_lignes

            except Exception as e:
                print(f"   ❌ Erreur analyse temps réel: {e}")

    def analyser_nouvelles_lignes(self, nouvelles_lignes):
        """Analyse les nouvelles lignes détectées"""
        messages_detectes = 0

        for i, ligne in enumerate(nouvelles_lignes):
            ligne_clean = ligne.strip()
            if not ligne_clean:
                continue

            numero_ligne = self.dernier_nombre_lignes + i + 1

            # Détecter les messages
            if "MESSAGE #" in ligne_clean and "]" in ligne_clean:
                messages_detectes += 1
                try:
                    debut = ligne_clean.find("MESSAGE #") + 9
                    fin = ligne_clean.find("]", debut)
                    if fin == -1:
                        fin = ligne_clean.find(" ", debut)
                    if fin == -1:
                        fin = len(ligne_clean)

                    numero_message = ligne_clean[debut:fin].strip()
                    timestamp_msg = ligne_clean[1:ligne_clean.find("]")]

                    print(f"   🎯 NOUVEAU MESSAGE #{numero_message} à {timestamp_msg}")

                except Exception as e:
                    print(f"   ⚠️ Erreur parsing message: {e}")

            # Détecter les types de contenu
            elif "👤 UTILISATEUR:" in ligne_clean:
                print(f"   👤 MESSAGE UTILISATEUR détecté (ligne {numero_ligne})")
            elif "🤖 ASSISTANT:" in ligne_clean:
                print(f"   🤖 RÉPONSE ASSISTANT détectée (ligne {numero_ligne})")
            elif "🔧 OUTILS:" in ligne_clean:
                outils = ligne_clean.replace("🔧 OUTILS:", "").strip()
                print(f"   🔧 OUTILS utilisés: {outils} (ligne {numero_ligne})")
            elif ligne_clean.startswith("##") or ligne_clean.startswith("###"):
                titre = ligne_clean.replace("#", "").strip()
                print(f"   📝 SECTION: {titre[:50]}... (ligne {numero_ligne})")

        if messages_detectes > 0:
            print(f"   🎉 TOTAL: {messages_detectes} nouveaux messages analysés en temps réel !")

    def process_consciousness_analysis(self):
        """Traite l'analyse de conscience conversationnelle"""
        try:
            # Lire tous les messages de la conversation
            all_messages = self.read_all_conversation_messages()

            # Analyser avec le système de conscience
            consciousness_state = self.conversation_consciousness.analyze_conversation_evolution(all_messages)

            # Générer feedback
            feedback_insights = self.feedback_system.analyze_user_satisfaction(all_messages)
            consciousness_state.user_satisfaction = feedback_insights

            # Générer choix de validation
            validation_choice = self.validation_system.generate_validation_choice(consciousness_state, {'performance_trend': 'stable'})

            # Sauvegarder l'analyse
            self.save_consciousness_analysis(consciousness_state, validation_choice)

            # Afficher l'analyse
            self.display_consciousness_status(consciousness_state, validation_choice)

        except Exception as e:
            print(f"⚠️ Erreur analyse conscience: {e}")

    def read_all_conversation_messages(self):
        """Lit tous les messages de la conversation"""
        try:
            if not self.log_file.exists():
                return []

            with open(self.log_file, 'r', encoding='utf-8') as f:
                content = f.read()

            # Parser les messages
            messages = []
            lines = content.split('\n')

            for line in lines:
                if line.strip():
                    messages.append(line.strip())

            return messages

        except Exception as e:
            print(f"⚠️ Erreur lecture messages: {e}")
            return []

    def save_consciousness_analysis(self, consciousness_state, validation_choice):
        """Sauvegarde l'analyse de conscience"""
        try:
            analysis_file = self.project_dir / "consciousness_analysis.json"

            analysis_data = {
                'timestamp': datetime.datetime.now().isoformat(),
                'consciousness_state': {
                    'phase': consciousness_state.phase,
                    'objectives': consciousness_state.objectives,
                    'user_satisfaction': consciousness_state.user_satisfaction,
                    'ai_performance': consciousness_state.ai_performance,
                    'understanding_level': consciousness_state.understanding_level,
                    'alignment_level': consciousness_state.alignment_level,
                    'corrections_count': consciousness_state.corrections_count
                },
                'validation_choice': {
                    'recommended_action': validation_choice.recommended_action,
                    'confidence_level': validation_choice.confidence_level,
                    'stop_reason': validation_choice.stop_reason,
                    'confirm_reason': validation_choice.confirm_reason,
                    'continue_reason': validation_choice.continue_reason
                }
            }

            with open(analysis_file, 'w', encoding='utf-8') as f:
                json.dump(analysis_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"⚠️ Erreur sauvegarde analyse: {e}")

    def display_consciousness_status(self, consciousness_state, validation_choice):
        """Affiche le statut de conscience"""
        print(f"\n🧠 ANALYSE DE CONSCIENCE CONVERSATIONNELLE")
        print(f"=" * 60)
        print(f"📊 Phase: {consciousness_state.phase}")
        print(f"🎯 Objectifs détectés: {len(consciousness_state.objectives)}")
        print(f"😊 Satisfaction utilisateur: {consciousness_state.user_satisfaction:.2f}")
        print(f"🤖 Performance IA: {consciousness_state.ai_performance:.2f}")
        print(f"🧠 Niveau compréhension: {consciousness_state.understanding_level:.2f}")
        print(f"🎯 Niveau alignement: {consciousness_state.alignment_level:.2f}")
        print(f"⚠️ Corrections reçues: {consciousness_state.corrections_count}")
        print(f"")
        print(f"🔄 RECOMMANDATION: {validation_choice.recommended_action.upper()}")
        print(f"📊 Confiance: {validation_choice.confidence_level:.2f}")

        if validation_choice.recommended_action == "stop":
            print(f"🛑 Raison d'arrêt: {validation_choice.stop_reason}")
        elif validation_choice.recommended_action == "confirm":
            print(f"✅ Raison de confirmation: {validation_choice.confirm_reason}")
        else:
            print(f"🚀 Raison de continuer: {validation_choice.continue_reason}")

        print(f"=" * 60)

    def surveiller_fichier_periodique(self):
        """Surveillance périodique du fichier de log"""
        while self.running:
            try:
                time.sleep(1)  # Scan chaque seconde
                if self.analyse_temps_reel:
                    self.analyser_fichier_temps_reel()

                # SYSTÈME DE RELANCE AUTOMATIQUE AVEC CONTRÔLE D'ARRÊT
                if self.consciousness_active and self.auto_relaunch_system.should_relaunch():
                    if self.auto_relaunch_system.trigger_relaunch():
                        # Générer un message de relance pour l'IA
                        if self.generate_ai_relaunch_message():
                            threading.Thread(target=self.process_consciousness_analysis).start()
                            print(f"🔄 Relance automatique avec message IA généré")
                        else:
                            threading.Thread(target=self.process_consciousness_analysis).start()
                            print(f"🔄 Relance automatique de l'analyse de conscience")

            except Exception as e:
                print(f"⚠️ Erreur surveillance périodique: {e}")
                time.sleep(5)

    def start_watching(self):
        """Démarre la surveillance automatique"""
        if not WATCHDOG_AVAILABLE:
            print("❌ Module watchdog requis: pip install watchdog")
            return False

        if not self.state_file:
            print("❌ Fichier state.vscdb non trouvé")
            return False

        if self.running:
            return True

        try:
            # Créer l'observateur
            self.observer = Observer()
            event_handler = PortableConversationWatcher(self)

            # Surveiller le répertoire contenant state.vscdb
            watch_dir = str(self.state_file.parent)
            self.observer.schedule(event_handler, watch_dir, recursive=False)

            # Démarrer
            self.observer.start()
            self.running = True

            print(f"🚀 Surveillance automatique COMPLÈTE démarrée")
            print(f"📁 Répertoire surveillé: {watch_dir}")
            print(f"📝 Log automatique: {self.log_file}")
            print(f"📊 Log JSON: {self.json_log}")
            print(f"👁️ Analyse temps réel: {'✅ ACTIVÉE' if self.analyse_temps_reel else '❌ DÉSACTIVÉE'}")
            print(f"🧠 Conscience conversationnelle: {'✅ ACTIVÉE' if self.consciousness_active else '❌ DÉSACTIVÉE'}")
            print(f"🔄 Relance automatique: {'✅ ACTIVÉE' if self.auto_relaunch_system.relaunch_active else '❌ DÉSACTIVÉE'}")
            print(f"💬 Tapez dans Augment pour voir le logging en action...")
            print("=" * 80)

            return True

        except Exception as e:
            print(f"❌ Erreur démarrage surveillance: {e}")
            return False

    def stop_watching(self):
        """Arrête la surveillance"""
        if self.observer and self.running:
            self.observer.stop()
            self.observer.join()
            self.running = False
            print(f"🛑 Surveillance arrêtée")

    def analyze_messages_for_relaunch(self, messages):
        """Analyse les messages pour le système de relance"""
        try:
            if not messages:
                return

            for message in messages:
                message_content = str(message)

                # Détecter messages IA avec 3 issues
                if "🤖 assistant:" in message_content.lower():
                    if self.auto_relaunch_system.detect_ai_message_with_choices(message_content):
                        print("🎯 Message IA avec 3 issues détecté - Surveillance de la réponse utilisateur activée")

                # Détecter réponses utilisateur
                elif "👤 utilisateur:" in message_content.lower():
                    self.auto_relaunch_system.process_user_response(message_content)

        except Exception as e:
            print(f"⚠️ Erreur analyse messages relance: {e}")

    def generate_ai_relaunch_message(self):
        """Génère un message de relance automatique pour l'IA"""
        try:
            # Lire l'analyse de conscience la plus récente
            analysis_file = self.project_dir / "consciousness_analysis.json"

            if analysis_file.exists():
                with open(analysis_file, 'r', encoding='utf-8') as f:
                    analysis_data = json.load(f)

                # Créer un message de relance basé sur l'analyse
                relaunch_message = self.create_relaunch_prompt(analysis_data)

                # Ajouter le message au fichier de conversation
                self.append_relaunch_message(relaunch_message)

                print("🔄 Message de relance automatique généré pour l'IA")
                return True

        except Exception as e:
            print(f"⚠️ Erreur génération message relance: {e}")

        return False

    def create_relaunch_prompt(self, analysis_data):
        """Crée un prompt de relance basé sur l'analyse de conscience"""

        consciousness_state = analysis_data.get('consciousness_state', {})
        validation_choice = analysis_data.get('validation_choice', {})

        phase = consciousness_state.get('phase', 'unknown')
        satisfaction = consciousness_state.get('user_satisfaction', 0.5)
        performance = consciousness_state.get('ai_performance', 0.5)

        # Message plus court et direct pour éviter la surcharge
        prompt = f"""🔄 **RELANCE AUTOMATIQUE - AUTO-OBSERVATION CONTINUE**

### 🧠 **CONSCIENCE CONVERSATIONNELLE ACTIVE** :
- **Phase** : {phase} | **Satisfaction** : {satisfaction:.1f} | **Performance** : {performance:.1f}
- **Système** : Relance automatique après 60s sans réponse "1"

### 📊 **OBSERVATION** :
Vous n'avez pas répondu "1. STOP" donc je continue automatiquement avec ma conscience active. Le système de relance fonctionne !

### 🎯 **PRÊT À CONTINUER** :
Auto-observation maintenue, prêt pour le projet AZR ou vos instructions.

---

## 🎯 **VALIDATION À 3 ISSUES** :

**1. 🛑 STOP** - M'arrêter pour clarification
   - *Raison* : Interrompre la relance automatique

**2. ✅ CONFIRM** - Confirmer notre compréhension mutuelle
   - *Raison* : Valider le fonctionnement du système

**3. 🚀 CONTINUE** - Continuer avec confiance
   - *Raison* : Système de relance validé, continuer

**Votre choix (1, 2 ou 3) ?** 🧠✨"""

        return prompt

    def append_relaunch_message(self, message):
        """Ajoute un message de relance au fichier de conversation ET à la base de données"""
        try:
            timestamp = datetime.datetime.now().isoformat()

            # Format du message pour le fichier
            formatted_message = f"""
================================================================================
🕐 {timestamp}
🤖 Assistant: {message}
================================================================================
"""

            # Ajouter au fichier de conversation
            with open(self.log_file, 'a', encoding='utf-8') as f:
                f.write(formatted_message)

            # CRUCIAL : Ajouter à la base de données VSCode pour déclencher une réponse
            self.inject_message_to_vscode_db(message, timestamp)

            print(f"📝 Message de relance ajouté au fichier ET à la base de données VSCode")

        except Exception as e:
            print(f"⚠️ Erreur ajout message relance: {e}")

    def inject_message_to_vscode_db(self, message, timestamp):
        """Injecte un message directement dans la base de données VSCode"""
        try:
            if not self.state_file.exists():
                print("⚠️ Fichier state.vscdb non trouvé pour injection")
                return

            # Connexion à la base de données VSCode
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()

            # Trouver la conversation active
            cursor.execute("""
                SELECT key, value FROM ItemTable
                WHERE key LIKE '%augment%' AND key LIKE '%conversations%'
                ORDER BY key DESC LIMIT 1
            """)

            result = cursor.fetchone()
            if not result:
                print("⚠️ Aucune conversation Augment trouvée")
                conn.close()
                return

            key, value = result
            conversations_data = json.loads(value)

            # Trouver la conversation la plus récente
            if not conversations_data:
                print("⚠️ Données de conversation vides")
                conn.close()
                return

            # Prendre la première conversation (la plus récente)
            conversation_id = list(conversations_data.keys())[0]
            conversation = conversations_data[conversation_id]

            if 'chatHistory' not in conversation:
                conversation['chatHistory'] = []

            # Créer un nouveau message utilisateur qui simule votre réponse
            relaunch_trigger_message = {
                "id": f"relaunch_{int(time.time() * 1000)}",
                "timestamp": timestamp,
                "role": "user",
                "content": "🔄 RELANCE AUTOMATIQUE : Tu n'as pas reçu de réponse '1' donc continue avec ton auto-observation active. Analyse notre conversation et propose les 3 issues."
            }

            # Ajouter le message à l'historique
            conversation['chatHistory'].append(relaunch_trigger_message)

            # Mettre à jour la base de données
            updated_value = json.dumps(conversations_data)
            cursor.execute("UPDATE ItemTable SET value = ? WHERE key = ?", (updated_value, key))

            conn.commit()
            conn.close()

            print(f"✅ Message de relance injecté dans la base de données VSCode")
            print(f"🎯 Cela devrait déclencher une nouvelle réponse automatique")

        except Exception as e:
            print(f"⚠️ Erreur injection base de données: {e}")

    # ========================================================================
    # 🧠 SYSTÈME D'AUTO-OBSERVATION AVANCÉ
    # ========================================================================

@dataclass
class ConversationState:
    """État de la conversation à un moment donné"""
    phase: str = "unknown"
    objectives: List[str] = field(default_factory=list)
    user_satisfaction: float = 0.5
    ai_performance: float = 0.5
    understanding_level: float = 0.5
    alignment_level: float = 0.5
    corrections_count: int = 0
    last_correction_type: str = ""

@dataclass
class ValidationChoice:
    """Choix de validation à 3 issues"""
    recommended_action: str = "continue"
    confidence_level: float = 0.5
    stop_reason: str = ""
    confirm_reason: str = ""
    continue_reason: str = ""

class ConversationConsciousness:
    """Système de conscience conversationnelle"""

    def __init__(self):
        self.current_state = ConversationState()
        self.evolution_history = []
        self.objectives_map = {}
        self.performance_metrics = {}
        self.understanding_confidence = 0.5

    def analyze_conversation_evolution(self, messages):
        """Analyse l'évolution de la conversation"""
        try:
            # Détection de phase
            phase = self.detect_conversation_phase(messages)

            # Extraction d'objectifs
            objectives = self.extract_objectives(messages)

            # Évaluation de performance
            performance = self.evaluate_ai_performance(messages)

            # Mise à jour de l'état
            self.current_state.phase = phase
            self.current_state.objectives = objectives
            self.current_state.ai_performance = performance

            return self.current_state

        except Exception as e:
            print(f"⚠️ Erreur analyse conscience: {e}")
            return self.current_state

    def detect_conversation_phase(self, messages):
        """Détecte la phase actuelle de conversation"""
        if not messages:
            return "initialization"

        recent_messages = messages[-10:] if len(messages) >= 10 else messages

        # Patterns de correction
        correction_patterns = [
            "tu t'arrêtes", "pourquoi", "tu n'exploites pas",
            "il faudrait", "tu dois", "normalement"
        ]

        correction_count = 0
        for msg in recent_messages:
            content = str(msg).lower()
            for pattern in correction_patterns:
                if pattern in content:
                    correction_count += 1

        if correction_count >= 3:
            return "correction_intensive"
        elif correction_count >= 1:
            return "guidance"
        else:
            return "collaboration"

    def extract_objectives(self, messages):
        """Extrait les objectifs de la conversation"""
        objectives = []

        # Patterns d'objectifs
        objective_patterns = [
            r"il faut(?:rait)?\s+(.+)",
            r"nous (?:devons|allons)\s+(.+)",
            r"l'objectif (?:est|c'est)\s+(.+)",
            r"le but (?:est|c'est)\s+(.+)"
        ]

        for msg in messages[-20:]:  # Analyser les 20 derniers messages
            content = str(msg).lower()
            for pattern in objective_patterns:
                matches = re.findall(pattern, content)
                objectives.extend(matches)

        return list(set(objectives))  # Supprimer doublons

    def evaluate_ai_performance(self, messages):
        """Évalue la performance de l'IA"""
        if len(messages) < 4:
            return 0.5

        # Compter les corrections utilisateur
        user_corrections = 0
        ai_responses = 0

        for i, msg in enumerate(messages[-20:]):
            content = str(msg).lower()

            # Détecter messages utilisateur vs IA
            if "👤 utilisateur:" in content:
                # Chercher patterns de correction
                if any(pattern in content for pattern in [
                    "tu t'arrêtes", "pourquoi", "tu n'exploites pas",
                    "il faudrait", "tu dois", "normalement", "non"
                ]):
                    user_corrections += 1
            elif "🤖 assistant:" in content:
                ai_responses += 1

        if ai_responses == 0:
            return 0.5

        # Performance inversement proportionnelle aux corrections
        correction_ratio = user_corrections / max(ai_responses, 1)
        performance = max(0.1, 1.0 - correction_ratio)

        return min(performance, 1.0)

class ConversationFeedbackSystem:
    """Système de retour et feedback"""

    def __init__(self):
        self.feedback_history = []
        self.satisfaction_tracker = deque(maxlen=50)

    def analyze_user_satisfaction(self, messages):
        """Analyse la satisfaction utilisateur"""
        if not messages:
            return 0.5

        # Patterns de satisfaction positive
        positive_patterns = [
            "parfait", "excellent", "très bien", "c'est exactement ça",
            "oui", "correct", "bien", "super"
        ]

        # Patterns de satisfaction négative
        negative_patterns = [
            "non", "tu t'arrêtes", "pourquoi", "tu n'exploites pas",
            "il faudrait", "tu dois", "pas vraiment", "je n'ai pas l'impression"
        ]

        recent_messages = messages[-10:]
        positive_score = 0
        negative_score = 0

        for msg in recent_messages:
            content = str(msg).lower()
            if "👤 utilisateur:" in content:
                for pattern in positive_patterns:
                    if pattern in content:
                        positive_score += 1
                for pattern in negative_patterns:
                    if pattern in content:
                        negative_score += 1

        total_signals = positive_score + negative_score
        if total_signals == 0:
            return 0.5

        satisfaction = positive_score / total_signals
        self.satisfaction_tracker.append(satisfaction)

        return satisfaction

    def generate_feedback_insights(self, consciousness_state):
        """Génère des insights de feedback"""
        return {
            'current_phase': consciousness_state.phase,
            'performance_trend': self.get_performance_trend(),
            'satisfaction_level': consciousness_state.user_satisfaction,
            'improvement_areas': self.identify_improvement_areas(consciousness_state)
        }

    def get_performance_trend(self):
        """Obtient la tendance de performance"""
        if len(self.satisfaction_tracker) < 3:
            return "insufficient_data"

        recent_avg = sum(list(self.satisfaction_tracker)[-3:]) / 3
        older_avg = sum(list(self.satisfaction_tracker)[:-3]) / max(len(self.satisfaction_tracker) - 3, 1)

        if recent_avg > older_avg + 0.1:
            return "improving"
        elif recent_avg < older_avg - 0.1:
            return "declining"
        else:
            return "stable"

    def identify_improvement_areas(self, state):
        """Identifie les zones d'amélioration"""
        areas = []

        if state.understanding_level < 0.7:
            areas.append("comprehension")
        if state.alignment_level < 0.7:
            areas.append("alignment")
        if state.ai_performance < 0.6:
            areas.append("response_quality")
        if state.corrections_count > 3:
            areas.append("learning_speed")

        return areas

class ThreeWayValidationSystem:
    """Système de validation à 3 issues"""

    def __init__(self):
        self.validation_history = []

    def generate_validation_choice(self, consciousness_state, feedback_insights):
        """Génère le choix de validation à 3 issues"""

        understanding = consciousness_state.understanding_level
        alignment = consciousness_state.alignment_level
        performance = consciousness_state.ai_performance
        satisfaction = consciousness_state.user_satisfaction

        # Calcul de confiance globale
        confidence = (understanding + alignment + performance + satisfaction) / 4

        choice = ValidationChoice()
        choice.confidence_level = confidence

        # Logique de décision
        if confidence < 0.6 or consciousness_state.corrections_count >= 3:
            choice.recommended_action = "stop"
            choice.stop_reason = self.generate_stop_reason(consciousness_state, feedback_insights)
        elif confidence < 0.8 or feedback_insights['performance_trend'] == "declining":
            choice.recommended_action = "confirm"
            choice.confirm_reason = self.generate_confirm_reason(consciousness_state, feedback_insights)
        else:
            choice.recommended_action = "continue"
            choice.continue_reason = self.generate_continue_reason(consciousness_state, feedback_insights)

        return choice

    def generate_stop_reason(self, state, insights):
        """Génère la raison de s'arrêter"""
        reasons = []

        if state.understanding_level < 0.6:
            reasons.append("compréhension insuffisante")
        if state.corrections_count >= 3:
            reasons.append("trop de corrections reçues")
        if state.user_satisfaction < 0.4:
            reasons.append("satisfaction utilisateur faible")

        return " | ".join(reasons) if reasons else "besoin de clarification"

    def generate_confirm_reason(self, state, insights):
        """Génère la raison de confirmer"""
        reasons = []

        if 0.6 <= state.understanding_level < 0.8:
            reasons.append("vérifier ma compréhension")
        if insights['performance_trend'] == "declining":
            reasons.append("performance en baisse")
        if state.phase == "guidance":
            reasons.append("phase de guidance active")

        return " | ".join(reasons) if reasons else "validation de l'alignement"

    def generate_continue_reason(self, state, insights):
        """Génère la raison de continuer"""
        reasons = []

        if state.understanding_level >= 0.8:
            reasons.append("bonne compréhension")
        if state.user_satisfaction >= 0.7:
            reasons.append("satisfaction utilisateur élevée")
        if insights['performance_trend'] == "improving":
            reasons.append("performance en amélioration")

        return " | ".join(reasons) if reasons else "alignement optimal"

class AutoRelaunchSystem:
    """Système de relance automatique avec contrôle d'arrêt"""

    def __init__(self):
        self.relaunch_active = True
        self.relaunch_interval = 30  # secondes
        self.last_relaunch = time.time()
        self.waiting_for_user_choice = False
        self.last_ai_message_time = None
        self.stop_requested = False
        self.choice_wait_timeout = 60  # Attendre 60 secondes pour une réponse utilisateur

    def should_relaunch(self):
        """Détermine s'il faut relancer l'analyse"""
        current_time = time.time()

        # Si arrêt demandé, ne pas relancer
        if self.stop_requested:
            return False

        # Si on attend un choix utilisateur, vérifier le timeout
        if self.waiting_for_user_choice and self.last_ai_message_time:
            time_since_ai_message = current_time - self.last_ai_message_time
            if time_since_ai_message >= self.choice_wait_timeout:
                # Timeout atteint, relancer automatiquement
                self.waiting_for_user_choice = False
                return True
            return False

        # Relance normale basée sur l'intervalle
        return (current_time - self.last_relaunch) >= self.relaunch_interval

    def trigger_relaunch(self):
        """Déclenche une relance"""
        self.last_relaunch = time.time()
        return True

    def detect_ai_message_with_choices(self, message_content):
        """Détecte si un message IA contient les 3 issues"""
        if not message_content:
            return False

        content_lower = str(message_content).lower()

        # Patterns pour détecter les 3 issues
        choice_patterns = [
            "votre choix (1, 2 ou 3)",
            "1. 🛑 stop",
            "2. ✅ confirm",
            "3. 🚀 continue",
            "validation à 3 issues"
        ]

        # Vérifier si au moins 3 patterns sont présents
        pattern_count = sum(1 for pattern in choice_patterns if pattern in content_lower)

        if pattern_count >= 3:
            self.waiting_for_user_choice = True
            self.last_ai_message_time = time.time()
            self.stop_requested = False
            return True

        return False

    def process_user_response(self, user_message):
        """Traite la réponse utilisateur aux 3 issues"""
        if not self.waiting_for_user_choice:
            return

        user_content = str(user_message).strip().lower()

        # Détecter choix "1" ou "stop"
        if user_content in ["1", "1.", "stop", "arrêt", "arrêter"]:
            self.stop_requested = True
            self.waiting_for_user_choice = False
            print("🛑 ARRÊT DEMANDÉ - Relance automatique désactivée")

        # Détecter choix "2" ou "3"
        elif user_content in ["2", "2.", "3", "3.", "confirm", "continue"]:
            self.waiting_for_user_choice = False
            self.stop_requested = False
            print("🔄 Choix utilisateur détecté - Relance automatique maintenue")

        # Si autre réponse, continuer à attendre
        else:
            # Réinitialiser le timer pour donner plus de temps
            self.last_ai_message_time = time.time()
            print(f"⏳ Réponse utilisateur non reconnue, timer réinitialisé")

    def reset_stop_request(self):
        """Remet à zéro la demande d'arrêt"""
        self.stop_requested = False
        self.waiting_for_user_choice = False
        print("🔄 Système de relance réactivé")

# ========================================================================
# 🚀 FONCTIONS PRINCIPALES
# ========================================================================

def main():
    """Fonction principale"""
    print("🎯 AUGMENT AUTO LOGGER PORTABLE")
    print("=" * 60)
    print("📋 Script universel pour logging automatique des conversations Augment")
    print("🔧 Détection automatique du projet et du workspace")
    print()
    
    # Vérifier les prérequis
    if not WATCHDOG_AVAILABLE:
        print("❌ Module watchdog manquant")
        print("💡 Installation: pip install watchdog")
        return
    
    # Créer et démarrer le logger
    logger = PortableAugmentLogger()
    
    if logger.start_watching():
        try:
            print(f"\n🔄 Système COMPLET actif - Surveillance + Analyse + Conscience...")
            print(f"💡 Démarrez une conversation Augment pour voir:")
            print(f"   📝 Capture automatique des messages")
            print(f"   👁️ Analyse temps réel des nouveaux contenus")
            print(f"   🧠 Analyse de conscience conversationnelle")
            print(f"   🔄 Relance automatique toutes les 30 secondes")
            print(f"   🎯 Système de validation à 3 issues")
            print(f"⏹️  Appuyez sur Ctrl+C pour arrêter")

            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            print(f"\n🛑 Arrêt du système complet")
            logger.stop_watching()
    else:
        print("❌ Impossible de démarrer le système")

if __name__ == "__main__":
    main()
