#!/usr/bin/env python3
"""
AUGMENT AUTO LOGGER PORTABLE - SCRIPT UNIVERSEL
===============================================

Script portable pour logging automatique des conversations Augment.
Fonctionne dans n'importe quel projet, détecte automatiquement le workspace.

UTILISATION:
1. Copiez ce fichier dans votre nouveau projet
2. Exécutez: python augment_auto_logger_portable.py
3. Le fichier 'augment_conversation_auto.txt' sera créé et mis à jour en temps réel

COMPORTEMENT:
- Détection automatique du workspace VSCode actuel
- Création du fichier de log dans le répertoire du projet
- Écriture en temps réel de toutes les conversations Augment
- Aucune configuration requise
"""

import sqlite3
import json
import datetime
import time
import threading
from pathlib import Path
import os
import sys

# Essayer d'importer watchdog
try:
    from watchdog.observers import Observer
    from watchdog.events import FileSystemEventHandler
    WATCHDOG_AVAILABLE = True
except ImportError:
    WATCHDOG_AVAILABLE = False
    print("⚠️ Module watchdog requis. Installation: pip install watchdog")

class PortableConversationWatcher(FileSystemEventHandler):
    def __init__(self, logger_instance):
        self.logger = logger_instance
        super().__init__()
    
    def on_modified(self, event):
        if event.is_directory:
            return
        
        if event.src_path.endswith('state.vscdb'):
            self.logger.process_file_change()

class PortableAugmentLogger:
    def __init__(self):
        # Détection automatique du répertoire de projet
        self.project_dir = self.detect_project_directory()
        
        # Détection automatique du fichier state.vscdb
        self.state_file = self.find_state_file()
        
        # Fichiers de log dans le projet actuel
        self.log_file = self.project_dir / "augment_conversation_auto.txt"
        self.json_log = self.project_dir / "augment_conversation_auto.json"
        
        self.last_message_count = 0
        self.last_conversation_id = None
        self.observer = None
        self.running = False
        
        print(f"🎯 AUGMENT AUTO LOGGER PORTABLE")
        print(f"=" * 50)
        print(f"📁 Projet détecté: {self.project_dir}")
        print(f"📄 State file: {self.state_file}")
        print(f"📝 Log file: {self.log_file}")
        
        # Initialiser les fichiers
        self.init_log_files()
        
        # Obtenir l'état initial
        self.get_initial_state()
    
    def detect_project_directory(self):
        """Détecte automatiquement le répertoire de projet"""
        # Méthode 1: Répertoire du script
        script_dir = Path(__file__).parent.absolute()
        
        # Méthode 2: Répertoire de travail actuel
        current_dir = Path.cwd()
        
        # Préférer le répertoire du script s'il semble être un projet
        if self.looks_like_project(script_dir):
            return script_dir
        elif self.looks_like_project(current_dir):
            return current_dir
        else:
            # Par défaut, utiliser le répertoire du script
            return script_dir
    
    def looks_like_project(self, directory):
        """Vérifie si un répertoire ressemble à un projet"""
        project_indicators = [
            '.git', '.vscode', 'package.json', 'requirements.txt', 
            'Cargo.toml', 'go.mod', 'pom.xml', 'build.gradle'
        ]
        
        for indicator in project_indicators:
            if (directory / indicator).exists():
                return True
        return False
    
    def find_state_file(self):
        """Trouve automatiquement le fichier state.vscdb du workspace actuel"""
        base_path = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage")
        
        if not base_path.exists():
            print(f"⚠️ Répertoire workspaceStorage non trouvé: {base_path}")
            return None
        
        # Chercher tous les fichiers state.vscdb
        state_files = []
        for workspace_dir in base_path.iterdir():
            if workspace_dir.is_dir():
                state_file = workspace_dir / "state.vscdb"
                if state_file.exists():
                    # Vérifier la date de modification (plus récent = plus probable)
                    mtime = state_file.stat().st_mtime
                    state_files.append((state_file, mtime))
        
        if not state_files:
            print(f"⚠️ Aucun fichier state.vscdb trouvé")
            return None
        
        # Trier par date de modification (plus récent en premier)
        state_files.sort(key=lambda x: x[1], reverse=True)
        
        # Prendre le plus récent qui contient des conversations Augment
        for state_file, _ in state_files:
            if self.has_augment_conversations(state_file):
                return state_file
        
        # Si aucun n'a de conversations Augment, prendre le plus récent
        return state_files[0][0]
    
    def has_augment_conversations(self, state_file):
        """Vérifie si un fichier state.vscdb contient des conversations Augment"""
        try:
            conn = sqlite3.connect(str(state_file))
            cursor = conn.cursor()
            cursor.execute("SELECT key FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()
            conn.close()
            return result is not None
        except Exception:
            return False
    
    def init_log_files(self):
        """Initialise les fichiers de log"""
        timestamp = datetime.datetime.now().isoformat()
        
        # Fichier texte
        with open(self.log_file, 'w', encoding='utf-8') as f:
            f.write("AUGMENT CONVERSATION - LOG AUTOMATIQUE\n")
            f.write(f"Démarré: {timestamp}\n")
            f.write(f"Projet: {self.project_dir}\n")
            f.write(f"Source: {self.state_file}\n")
            f.write("=" * 80 + "\n\n")
        
        # Fichier JSON
        initial_data = {
            "started_at": timestamp,
            "project_directory": str(self.project_dir),
            "source_file": str(self.state_file),
            "conversation_id": None,
            "messages": [],
            "total_messages": 0
        }
        with open(self.json_log, 'w', encoding='utf-8') as f:
            json.dump(initial_data, f, indent=2, ensure_ascii=False)
    
    def get_initial_state(self):
        """Obtient l'état initial de la conversation"""
        try:
            conversation_data = self.extract_conversation_data()
            if conversation_data:
                conversations = conversation_data.get('conversations', {})
                current_conv_id = conversation_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    self.last_message_count = len(chat_history)
                    self.last_conversation_id = current_conv_id
                    
                    print(f"📊 État initial: {self.last_message_count} messages")
        except Exception as e:
            print(f"⚠️ Erreur état initial: {e}")
    
    def extract_conversation_data(self):
        """Extrait les données de conversation"""
        try:
            if not self.state_file or not self.state_file.exists():
                return None
            
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat';")
            result = cursor.fetchone()
            
            if not result:
                conn.close()
                return None
            
            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conn.close()
            return webview_data
            
        except Exception as e:
            return None
    
    def format_message(self, message_data, index):
        """Formate un message pour le log"""
        timestamp = datetime.datetime.now().isoformat()
        
        # Extraire contenu utilisateur
        user_content = message_data.get('request_message', '')
        
        # Extraire réponse assistant
        assistant_content = ""
        tools_used = []
        
        if 'structured_output_nodes' in message_data:
            for node in message_data['structured_output_nodes']:
                if node.get('type') == 0 and node.get('content'):
                    assistant_content = node['content']
                elif node.get('type') == 5 and node.get('tool_use'):
                    tool_info = node['tool_use']
                    tools_used.append(tool_info.get('tool_name', 'unknown'))
        
        return {
            'index': index,
            'timestamp': timestamp,
            'user_message': user_content,
            'assistant_response': assistant_content,
            'tools_used': tools_used,
            'request_id': message_data.get('request_id', ''),
            'message_timestamp': message_data.get('timestamp', '')
        }
    
    def append_new_messages(self, new_messages):
        """Ajoute les nouveaux messages aux logs"""
        if not new_messages:
            return
        
        # Log texte - écriture immédiate
        with open(self.log_file, 'a', encoding='utf-8') as f:
            for msg in new_messages:
                f.write(f"[{msg['timestamp']}] MESSAGE #{msg['index']}\n")
                f.write("-" * 60 + "\n")
                
                if msg['user_message']:
                    f.write(f"👤 UTILISATEUR:\n{msg['user_message']}\n\n")
                
                if msg['assistant_response']:
                    f.write(f"🤖 ASSISTANT:\n{msg['assistant_response']}\n\n")
                
                if msg['tools_used']:
                    f.write(f"🔧 OUTILS: {', '.join(msg['tools_used'])}\n\n")
                
                f.write("=" * 80 + "\n\n")
            
            # Forcer l'écriture immédiate
            f.flush()
            os.fsync(f.fileno())
        
        # Log JSON - mise à jour
        try:
            with open(self.json_log, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            data['messages'].extend(new_messages)
            data['last_update'] = datetime.datetime.now().isoformat()
            data['total_messages'] = len(data['messages'])
            data['conversation_id'] = self.last_conversation_id
            
            with open(self.json_log, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            pass
        
        print(f"✅ {len(new_messages)} nouveaux messages loggés automatiquement")
    
    def process_file_change(self):
        """Traite un changement de fichier détecté"""
        try:
            # Petite pause pour éviter les lectures partielles
            time.sleep(0.1)
            
            conversation_data = self.extract_conversation_data()
            
            if conversation_data:
                conversations = conversation_data.get('conversations', {})
                current_conv_id = conversation_data.get('currentConversationId')
                
                if current_conv_id and current_conv_id in conversations:
                    chat_history = conversations[current_conv_id].get('chatHistory', [])
                    
                    # Vérifier s'il y a de nouveaux messages
                    if len(chat_history) > self.last_message_count:
                        new_messages_raw = chat_history[self.last_message_count:]
                        new_messages = []
                        
                        for i, msg_data in enumerate(new_messages_raw):
                            formatted_msg = self.format_message(
                                msg_data, 
                                self.last_message_count + i + 1
                            )
                            new_messages.append(formatted_msg)
                        
                        # Écrire immédiatement dans les logs
                        self.append_new_messages(new_messages)
                        
                        self.last_message_count = len(chat_history)
                        self.last_conversation_id = current_conv_id
            
        except Exception as e:
            print(f"⚠️ Erreur traitement changement: {e}")
    
    def start_watching(self):
        """Démarre la surveillance automatique"""
        if not WATCHDOG_AVAILABLE:
            print("❌ Module watchdog requis: pip install watchdog")
            return False
        
        if not self.state_file:
            print("❌ Fichier state.vscdb non trouvé")
            return False
        
        if self.running:
            return True
        
        try:
            # Créer l'observateur
            self.observer = Observer()
            event_handler = PortableConversationWatcher(self)
            
            # Surveiller le répertoire contenant state.vscdb
            watch_dir = str(self.state_file.parent)
            self.observer.schedule(event_handler, watch_dir, recursive=False)
            
            # Démarrer
            self.observer.start()
            self.running = True
            
            print(f"🚀 Surveillance automatique démarrée")
            print(f"📁 Répertoire surveillé: {watch_dir}")
            print(f"📝 Log automatique: {self.log_file}")
            print(f"📊 Log JSON: {self.json_log}")
            print(f"💬 Tapez dans Augment pour voir le logging en action...")
            
            return True
            
        except Exception as e:
            print(f"❌ Erreur démarrage surveillance: {e}")
            return False
    
    def stop_watching(self):
        """Arrête la surveillance"""
        if self.observer and self.running:
            self.observer.stop()
            self.observer.join()
            self.running = False
            print(f"🛑 Surveillance arrêtée")

def main():
    """Fonction principale"""
    print("🎯 AUGMENT AUTO LOGGER PORTABLE")
    print("=" * 60)
    print("📋 Script universel pour logging automatique des conversations Augment")
    print("🔧 Détection automatique du projet et du workspace")
    print()
    
    # Vérifier les prérequis
    if not WATCHDOG_AVAILABLE:
        print("❌ Module watchdog manquant")
        print("💡 Installation: pip install watchdog")
        return
    
    # Créer et démarrer le logger
    logger = PortableAugmentLogger()
    
    if logger.start_watching():
        try:
            print(f"\n🔄 Système actif - Surveillance en cours...")
            print(f"💡 Démarrez une conversation Augment pour voir le logging")
            print(f"⏹️  Appuyez sur Ctrl+C pour arrêter")
            
            while True:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Arrêt du logging automatique")
            logger.stop_watching()
    else:
        print("❌ Impossible de démarrer le système")

if __name__ == "__main__":
    main()
