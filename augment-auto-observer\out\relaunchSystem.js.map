{"version": 3, "file": "relaunchSystem.js", "sourceRoot": "", "sources": ["../src/relaunchSystem.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAGjC,MAAa,cAAc;IAYvB,YAAY,OAAgC;QAVpC,aAAQ,GAAG,KAAK,CAAC;QAEjB,yBAAoB,GAAG,KAAK,CAAC;QAC7B,kBAAa,GAAG,KAAK,CAAC;QACtB,sBAAiB,GAAG,EAAE,CAAC,CAAC,cAAc;QACtC,qBAAgB,GAAG,EAAE,CAAC,CAAC,WAAW;QAMtC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QAEvB,wBAAwB;QACxB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1E,IAAI,CAAC,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,CAAC,CAAC;QAC7D,IAAI,CAAC,gBAAgB,GAAG,MAAM,CAAC,GAAG,CAAC,kBAAkB,EAAE,EAAE,CAAC,CAAC;IAC/D,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QACrB,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,OAAO,CAAC,GAAG,CAAC,2BAA2B,CAAC,CAAC;IAC7C,CAAC;IAED,IAAI;QACA,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;YAChB,OAAO;SACV;QAED,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QAEtB,IAAI,IAAI,CAAC,eAAe,EAAE;YACtB,aAAa,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YACpC,IAAI,CAAC,eAAe,GAAG,SAAS,CAAC;SACpC;QAED,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,CAAC;IAC5C,CAAC;IAED,gBAAgB,CAAC,QAAoB;QACjC,IAAI,CAAC,wBAAwB,GAAG,QAAQ,CAAC;IAC7C,CAAC;IAED,eAAe,CAAC,QAA+B;QAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzC,OAAO;SACV;QAED,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,qCAAqC;YACrC,IAAI,OAAO,CAAC,iBAAiB,IAAI,IAAI,CAAC,wBAAwB,CAAC,OAAO,CAAC,iBAAiB,CAAC,EAAE;gBACvF,IAAI,CAAC,oBAAoB,GAAG,IAAI,CAAC;gBACjC,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACpC,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAE3B,OAAO,CAAC,GAAG,CAAC,4DAA4D,CAAC,CAAC;gBAE1E,qCAAqC;gBACrC,IAAI,CAAC,0BAA0B,EAAE,CAAC;aACrC;YAED,gCAAgC;YAChC,IAAI,OAAO,CAAC,WAAW,EAAE;gBACrB,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC;aAChD;SACJ;IACL,CAAC;IAED,iBAAiB,CAAC,MAAc;QAC5B,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC,EAAE,OAAO;gBACX,IAAI,CAAC,aAAa,GAAG,IAAI,CAAC;gBAC1B,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;gBACzE,MAAM;YAEV,KAAK,CAAC,EAAE,UAAU;gBACd,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAC/C,MAAM;YAEV,KAAK,CAAC,EAAE,WAAW;gBACf,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC3B,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACtD,MAAM;SACb;IACL,CAAC;IAEO,wBAAwB,CAAC,OAAe;QAC5C,MAAM,QAAQ,GAAG;YACb,yBAAyB;YACzB,OAAO;YACP,MAAM;YACN,OAAO;YACP,uBAAuB;SAC1B,CAAC;QAEF,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE;gBAC9C,YAAY,EAAE,CAAC;aAClB;SACJ;QAED,OAAO,YAAY,IAAI,CAAC,CAAC;IAC7B,CAAC;IAEO,kBAAkB,CAAC,WAAmB;QAC1C,IAAI,CAAC,IAAI,CAAC,oBAAoB,EAAE;YAC5B,OAAO;SACV;QAED,MAAM,OAAO,GAAG,WAAW,CAAC,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAEjD,+BAA+B;QAC/B,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC3D,IAAI,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAAC;SAC7B;QACD,4BAA4B;aACvB,IAAI,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YACtE,MAAM,MAAM,GAAG,CAAC,GAAG,EAAE,IAAI,EAAE,SAAS,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;YAChE,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,CAAC;SAClC;QACD,2CAA2C;aACtC;YACD,IAAI,CAAC,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YACpC,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;SACzE;IACL,CAAC;IAEO,eAAe;QACnB,iCAAiC;QACjC,IAAI,CAAC,eAAe,GAAG,WAAW,CAAC,GAAG,EAAE;YACpC,IAAI,CAAC,gBAAgB,EAAE,CAAC;QAC5B,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEO,gBAAgB;QACpB,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzE,OAAO;SACV;QAED,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC/B,MAAM,eAAe,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;QAEtE,wDAAwD;QACxD,IAAI,IAAI,CAAC,aAAa,EAAE;YACpB,IAAI,eAAe,IAAI,GAAG,EAAE,EAAE,YAAY;gBACtC,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACtD,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;gBAC3B,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;aACrC;YACD,OAAO;SACV;QAED,gCAAgC;QAChC,IAAI,eAAe,IAAI,IAAI,CAAC,iBAAiB,EAAE;YAC3C,oBAAoB;YACpB,IAAI,IAAI,CAAC,gBAAgB,EAAE;gBACvB,MAAM,qBAAqB,GAAG,CAAC,WAAW,GAAG,IAAI,CAAC,gBAAgB,CAAC,GAAG,IAAI,CAAC;gBAC3E,IAAI,qBAAqB,GAAG,IAAI,CAAC,gBAAgB,EAAE;oBAC/C,OAAO,CAAC,GAAG,CAAC,0BAA0B,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,gBAAgB,GAAG,qBAAqB,CAAC,cAAc,CAAC,CAAC;oBAC/G,OAAO;iBACV;aACJ;YAED,OAAO,CAAC,GAAG,CAAC,yBAAyB,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,2BAA2B,CAAC,CAAC;YAC7F,IAAI,CAAC,eAAe,EAAE,CAAC;SAC1B;IACL,CAAC;IAEO,eAAe;QACnB,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACnC,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAElC,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;QAEpD,gCAAgC;QAChC,IAAI,IAAI,CAAC,wBAAwB,EAAE;YAC/B,IAAI,CAAC,wBAAwB,EAAE,CAAC;SACnC;QAED,wBAAwB;QACxB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,+CAA+C,EAC/C,aAAa,CAChB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,IAAI,SAAS,KAAK,aAAa,EAAE;gBAC7B,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,kCAAkC,CAAC,CAAC;aACtE;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAEO,0BAA0B;QAC9B,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,uBAAuB,CAAC,CAAC;QAC1E,MAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;QAEpE,IAAI,CAAC,mBAAmB,EAAE;YACtB,OAAO;SACV;QAED,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,wCAAwC,EACxC,YAAY,EACZ,cAAc,EACd,gBAAgB,CACnB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,QAAQ,SAAS,EAAE;gBACf,KAAK,YAAY;oBACb,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBAChE,MAAM;gBACV,KAAK,cAAc;oBACf,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBAChE,MAAM;gBACV,KAAK,gBAAgB;oBACjB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBAChE,MAAM;aACb;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED,sCAAsC;IACtC,kBAAkB;QACd,OAAO,IAAI,CAAC,oBAAoB,CAAC;IACrC,CAAC;IAED,eAAe;QACX,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B,CAAC;IAED,qBAAqB;QACjB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACzB,OAAO,CAAC,CAAC;SACZ;QACD,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;IACxD,CAAC;IAED,oBAAoB;QAChB,IAAI,CAAC,IAAI,CAAC,oBAAoB,IAAI,CAAC,IAAI,CAAC,iBAAiB,EAAE;YACvD,OAAO,CAAC,CAAC;SACZ;QAED,MAAM,eAAe,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;QACrD,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC,iBAAiB,GAAG,eAAe,CAAC,CAAC;IACjE,CAAC;IAED,gBAAgB;QACZ,IAAI,CAAC,aAAa,GAAG,KAAK,CAAC;QAC3B,IAAI,CAAC,oBAAoB,GAAG,KAAK,CAAC;QAClC,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAC/D,CAAC;CACJ;AAvQD,wCAuQC"}