#!/usr/bin/env python3
"""
ANALYSEUR TEMPS RÉEL - FICHIER CONVERSATION
============================================

Surveille en temps réel le fichier augment_conversation_auto.txt
et affiche immédiatement les nouveaux messages détectés.
"""

import os
import time
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class AnalyseurTempsReelConversation(FileSystemEventHandler):
    def __init__(self, fichier_conversation):
        self.fichier_conversation = fichier_conversation
        self.derniere_taille = 0
        self.dernier_nombre_lignes = 0
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        
        # Initialiser l'état
        self.init_etat_initial()
        
    def init_etat_initial(self):
        """Initialise l'état du fichier"""
        try:
            if os.path.exists(self.fichier_conversation):
                stat = os.stat(self.fichier_conversation)
                self.derniere_taille = stat.st_size
                
                with open(self.fichier_conversation, 'r', encoding='utf-8') as f:
                    lignes = f.readlines()
                    self.dernier_nombre_lignes = len(lignes)
                
                print(f"📊 État initial: {self.derniere_taille} bytes, {self.dernier_nombre_lignes} lignes")
            else:
                print(f"⚠️ Fichier non trouvé: {self.fichier_conversation}")
                self.derniere_taille = 0
                self.dernier_nombre_lignes = 0
                
        except Exception as e:
            print(f"❌ Erreur initialisation: {e}")
            self.derniere_taille = 0
            self.dernier_nombre_lignes = 0
    
    def on_modified(self, event):
        """Détection de modification du fichier"""
        if event.is_directory:
            return
        
        if os.path.abspath(event.src_path) == os.path.abspath(self.fichier_conversation):
            timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
            print(f"\n🔥 [{timestamp}] FICHIER MODIFIÉ !")
            
            # Analyser les changements
            threading.Thread(target=self.analyser_changements).start()
    
    def analyser_changements(self):
        """Analyse les changements dans le fichier"""
        with self.lock:
            try:
                # Petite pause pour éviter les lectures partielles
                time.sleep(0.1)
                
                if not os.path.exists(self.fichier_conversation):
                    print("   ⚠️ Fichier non accessible")
                    return
                
                # Obtenir les nouvelles informations
                stat = os.stat(self.fichier_conversation)
                nouvelle_taille = stat.st_size
                
                with open(self.fichier_conversation, 'r', encoding='utf-8') as f:
                    lignes = f.readlines()
                    nouveau_nombre_lignes = len(lignes)
                
                # Analyser les changements
                delta_taille = nouvelle_taille - self.derniere_taille
                delta_lignes = nouveau_nombre_lignes - self.dernier_nombre_lignes
                
                timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                
                print(f"   📊 [{timestamp}] Taille: {self.derniere_taille} → {nouvelle_taille} (+{delta_taille} bytes)")
                print(f"   📄 Lignes: {self.dernier_nombre_lignes} → {nouveau_nombre_lignes} (+{delta_lignes} lignes)")
                
                # Afficher les nouvelles lignes
                if delta_lignes > 0:
                    print(f"   ✨ NOUVELLES LIGNES DÉTECTÉES:")
                    nouvelles_lignes = lignes[self.dernier_nombre_lignes:]
                    
                    for i, ligne in enumerate(nouvelles_lignes):
                        ligne_clean = ligne.strip()
                        if ligne_clean:  # Ignorer les lignes vides
                            numero_ligne = self.dernier_nombre_lignes + i + 1
                            print(f"      [{numero_ligne:3d}] {ligne_clean}")
                    
                    # Détecter les nouveaux messages
                    self.detecter_nouveaux_messages(nouvelles_lignes)
                
                # Mettre à jour l'état
                self.derniere_taille = nouvelle_taille
                self.dernier_nombre_lignes = nouveau_nombre_lignes
                
            except Exception as e:
                print(f"   ❌ Erreur analyse: {e}")
    
    def detecter_nouveaux_messages(self, nouvelles_lignes):
        """Détecte et analyse les nouveaux messages"""
        messages_detectes = 0
        
        for ligne in nouvelles_lignes:
            ligne_clean = ligne.strip()
            
            # Détecter les messages
            if "MESSAGE #" in ligne_clean and "]" in ligne_clean:
                messages_detectes += 1
                # Extraire le numéro de message
                try:
                    debut = ligne_clean.find("MESSAGE #") + 9
                    fin = ligne_clean.find("]", debut)
                    if fin == -1:
                        fin = ligne_clean.find(" ", debut)
                    if fin == -1:
                        fin = len(ligne_clean)
                    
                    numero_message = ligne_clean[debut:fin].strip()
                    timestamp_msg = ligne_clean[1:ligne_clean.find("]")]
                    
                    print(f"      🎯 NOUVEAU MESSAGE #{numero_message} à {timestamp_msg}")
                    
                except Exception as e:
                    print(f"      ⚠️ Erreur parsing message: {e}")
            
            # Détecter les types de contenu
            elif "👤 UTILISATEUR:" in ligne_clean:
                print(f"      👤 MESSAGE UTILISATEUR détecté")
            elif "🤖 ASSISTANT:" in ligne_clean:
                print(f"      🤖 RÉPONSE ASSISTANT détectée")
            elif "🔧 OUTILS:" in ligne_clean:
                outils = ligne_clean.replace("🔧 OUTILS:", "").strip()
                print(f"      🔧 OUTILS utilisés: {outils}")
        
        if messages_detectes > 0:
            print(f"   🎉 TOTAL: {messages_detectes} nouveaux messages détectés !")
    
    def scan_periodique(self):
        """Scan périodique pour détecter les changements même sans événement filesystem"""
        while self.running:
            try:
                time.sleep(2)  # Scan toutes les 2 secondes
                
                if os.path.exists(self.fichier_conversation):
                    stat = os.stat(self.fichier_conversation)
                    nouvelle_taille = stat.st_size
                    
                    if nouvelle_taille != self.derniere_taille:
                        timestamp = datetime.now().strftime('%H:%M:%S.%f')[:-3]
                        print(f"\n🔍 [{timestamp}] SCAN PÉRIODIQUE - Changement détecté")
                        self.analyser_changements()
                
            except Exception as e:
                print(f"⚠️ Erreur scan périodique: {e}")
                time.sleep(5)
    
    def demarrer_surveillance(self):
        """Démarre la surveillance temps réel"""
        print("🚀 ANALYSEUR TEMPS RÉEL - CONVERSATION AUGMENT")
        print("=" * 60)
        print(f"📁 Fichier surveillé: {self.fichier_conversation}")
        print(f"📊 État initial: {self.derniere_taille} bytes, {self.dernier_nombre_lignes} lignes")
        print("🔍 SURVEILLANCE ACTIVE :")
        print("   • Détection filesystem instantanée")
        print("   • Scan périodique toutes les 2 secondes")
        print("   • Analyse des nouveaux messages")
        print("   • Affichage temps réel des changements")
        print("💬 Écrivez dans Augment pour voir la détection...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Démarrer le scan périodique
        scan_thread = threading.Thread(target=self.scan_periodique)
        scan_thread.daemon = True
        scan_thread.start()
        
        # Surveillance filesystem
        repertoire = os.path.dirname(os.path.abspath(self.fichier_conversation))
        self.observer = Observer()
        self.observer.schedule(self, repertoire, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                time.sleep(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'analyseur temps réel...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

def main():
    """Fonction principale"""
    fichier_conversation = r"C:\Users\<USER>\Desktop\2\augment_conversation_auto.txt"
    
    print(f"🎯 ANALYSEUR TEMPS RÉEL - CONVERSATION AUGMENT")
    print(f"📁 Fichier cible: {fichier_conversation}")
    
    if not os.path.exists(fichier_conversation):
        print(f"❌ Fichier non trouvé: {fichier_conversation}")
        print(f"💡 Assurez-vous que le système de capture Augment est actif")
        return
    
    analyseur = AnalyseurTempsReelConversation(fichier_conversation)
    analyseur.demarrer_surveillance()

if __name__ == "__main__":
    main()
