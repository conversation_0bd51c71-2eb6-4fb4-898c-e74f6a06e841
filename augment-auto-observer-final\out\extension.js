"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deactivate = exports.activate = void 0;
const vscode = require("vscode");
const fs = require("fs");
const path = require("path");
const chokidar = require("chokidar");
function activate(context) {
    console.log('🚀 Augment Auto Observer Final - Activation');
    let isActive = false;
    let statusBarItem;
    let fileWatcher;
    let conversationData = {
        totalMessages: 0,
        lastAIMessage: '',
        lastUserMessage: '',
        lastMessageTime: new Date(),
        conversationContent: ''
    };
    let inactivityTimer;
    let conversationPanel;
    // Configuration
    const config = vscode.workspace.getConfiguration('augment-auto-observer-final');
    const inactivityTimeout = config.get('inactivityTimeout', 30) * 1000; // en ms
    const enableNotifications = config.get('enableNotifications', true);
    const autoStart = config.get('autoStart', true);
    // <PERSON><PERSON>er la barre de statut
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    statusBarItem.command = 'augment-auto-observer-final.showConversation';
    context.subscriptions.push(statusBarItem);
    // Commande: Afficher conversation
    const showConversationCommand = vscode.commands.registerCommand('augment-auto-observer-final.showConversation', () => {
        showConversationPanel();
    });
    // Commande: Toggle auto-observation
    const toggleCommand = vscode.commands.registerCommand('augment-auto-observer-final.toggleAutoObserver', () => {
        if (isActive) {
            stopAutoObserver();
        }
        else {
            startAutoObserver();
        }
    });
    // Commande: Forcer relance
    const forceRelaunchCommand = vscode.commands.registerCommand('augment-auto-observer-final.forceRelaunch', () => {
        triggerAIRelaunch('Manuel');
    });
    function startAutoObserver() {
        if (isActive)
            return;
        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                vscode.window.showErrorMessage('Aucun workspace ouvert');
                return;
            }
            const logFile = path.join(workspaceFolder.uri.fsPath, 'augment_conversation_auto.txt');
            if (!fs.existsSync(logFile)) {
                vscode.window.showErrorMessage('Fichier augment_conversation_auto.txt non trouvé. Démarrez le script Python.');
                return;
            }
            // Lire le contenu initial
            loadConversationData(logFile);
            // Démarrer la surveillance
            fileWatcher = chokidar.watch(logFile, {
                persistent: true,
                ignoreInitial: true
            });
            fileWatcher.on('change', () => {
                loadConversationData(logFile);
                resetInactivityTimer();
            });
            isActive = true;
            updateStatusBar();
            startInactivityTimer();
            if (enableNotifications) {
                vscode.window.showInformationMessage('🧠 Auto-observation activée !');
            }
            console.log('✅ Auto-observation démarrée');
        }
        catch (error) {
            vscode.window.showErrorMessage(`Erreur démarrage: ${error}`);
        }
    }
    function stopAutoObserver() {
        if (!isActive)
            return;
        if (fileWatcher) {
            fileWatcher.close();
            fileWatcher = undefined;
        }
        if (inactivityTimer) {
            clearTimeout(inactivityTimer);
            inactivityTimer = undefined;
        }
        isActive = false;
        updateStatusBar();
        if (enableNotifications) {
            vscode.window.showInformationMessage('🛑 Auto-observation arrêtée');
        }
        console.log('🛑 Auto-observation arrêtée');
    }
    function loadConversationData(logFile) {
        try {
            const content = fs.readFileSync(logFile, 'utf8');
            const lines = content.split('\n');
            // Compter les messages
            const messageLines = lines.filter(line => line.includes('MESSAGE #'));
            const totalMessages = messageLines.length;
            // Extraire le dernier message IA et utilisateur
            let lastAIMessage = '';
            let lastUserMessage = '';
            let lastMessageTime = new Date();
            // Parcourir les lignes pour trouver les derniers messages
            for (let i = lines.length - 1; i >= 0; i--) {
                const line = lines[i];
                if (line.includes('🤖 ASSISTANT:') && !lastAIMessage) {
                    // Chercher le contenu du message IA
                    for (let j = i + 1; j < lines.length && j < i + 10; j++) {
                        if (lines[j].trim() && !lines[j].includes('🔧 OUTILS:') && !lines[j].includes('====')) {
                            lastAIMessage = lines[j].trim();
                            break;
                        }
                    }
                }
                if (line.includes('👤 UTILISATEUR:') && !lastUserMessage) {
                    // Chercher le contenu du message utilisateur
                    for (let j = i + 1; j < lines.length && j < i + 5; j++) {
                        if (lines[j].trim() && !lines[j].includes('====')) {
                            lastUserMessage = lines[j].trim();
                            break;
                        }
                    }
                }
                // Extraire timestamp du dernier message
                if (line.includes('[') && line.includes(']') && line.includes('MESSAGE #')) {
                    const timestampMatch = line.match(/\[(.*?)\]/);
                    if (timestampMatch) {
                        try {
                            lastMessageTime = new Date(timestampMatch[1]);
                        }
                        catch (e) {
                            // Garder la date actuelle si parsing échoue
                        }
                    }
                    break;
                }
            }
            // Mettre à jour les données
            const previousTotal = conversationData.totalMessages;
            conversationData = {
                totalMessages,
                lastAIMessage,
                lastUserMessage,
                lastMessageTime,
                conversationContent: content
            };
            // Détecter nouveaux messages
            if (totalMessages > previousTotal) {
                console.log(`📊 Nouveaux messages détectés: ${totalMessages - previousTotal}`);
                // Mettre à jour le panel si ouvert
                if (conversationPanel) {
                    updateConversationPanel();
                }
            }
            updateStatusBar();
        }
        catch (error) {
            console.error('Erreur lecture conversation:', error);
        }
    }
    function startInactivityTimer() {
        if (inactivityTimer) {
            clearTimeout(inactivityTimer);
        }
        inactivityTimer = setTimeout(() => {
            if (isActive) {
                console.log('⏰ Timeout d\'inactivité atteint');
                triggerAIRelaunch('Inactivité');
            }
        }, inactivityTimeout);
    }
    function resetInactivityTimer() {
        if (isActive) {
            startInactivityTimer();
        }
    }
    function triggerAIRelaunch(reason) {
        console.log(`🔄 Déclenchement relance IA: ${reason}`);
        // Méthode 1: Notification avec action
        if (enableNotifications) {
            vscode.window.showInformationMessage(`🔄 Relance IA (${reason}) - Continue ton auto-observation`, 'Ouvrir Chat', 'Voir Conversation').then(selection => {
                switch (selection) {
                    case 'Ouvrir Chat':
                        vscode.commands.executeCommand('augment.newChat');
                        break;
                    case 'Voir Conversation':
                        showConversationPanel();
                        break;
                }
            });
        }
        // Méthode 2: Essayer d'ouvrir Augment
        try {
            vscode.commands.executeCommand('augment.newChat');
        }
        catch (error) {
            console.error('Erreur ouverture Augment:', error);
        }
        // Méthode 3: Créer un fichier signal
        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (workspaceFolder) {
                const signalFile = path.join(workspaceFolder.uri.fsPath, 'ai_relaunch_signal.txt');
                const signalContent = `RELANCE IA - ${new Date().toISOString()}\nRaison: ${reason}\nMessage: Continue ton auto-observation de notre conversation.\n`;
                fs.writeFileSync(signalFile, signalContent, 'utf8');
                console.log('📝 Fichier signal créé:', signalFile);
            }
        }
        catch (error) {
            console.error('Erreur création fichier signal:', error);
        }
        // Redémarrer le timer
        resetInactivityTimer();
    }
    function updateStatusBar() {
        if (isActive) {
            statusBarItem.text = `$(eye) Auto-Obs: ${conversationData.totalMessages} msgs`;
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
            statusBarItem.tooltip = `Auto-observation active\nDerniers messages: ${conversationData.totalMessages}\nDernier: ${conversationData.lastMessageTime.toLocaleTimeString()}`;
        }
        else {
            statusBarItem.text = '$(eye-closed) Auto-Obs: OFF';
            statusBarItem.backgroundColor = undefined;
            statusBarItem.tooltip = 'Auto-observation arrêtée';
        }
        statusBarItem.show();
    }
    function showConversationPanel() {
        if (conversationPanel) {
            conversationPanel.reveal();
            return;
        }
        conversationPanel = vscode.window.createWebviewPanel('augmentConversation', '🧠 Auto-Observation - Conversation', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true
        });
        conversationPanel.onDidDispose(() => {
            conversationPanel = undefined;
        });
        updateConversationPanel();
    }
    function updateConversationPanel() {
        if (!conversationPanel)
            return;
        const html = generateConversationHTML();
        conversationPanel.webview.html = html;
    }
    function generateConversationHTML() {
        const content = conversationData.conversationContent
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/\n/g, '<br>');
        return `<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Auto-Observation</title>
    <style>
        body {
            font-family: 'Consolas', monospace;
            margin: 20px;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
            line-height: 1.4;
        }
        .header {
            background-color: var(--vscode-panel-background);
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid var(--vscode-panel-border);
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-bottom: 20px;
        }
        .stat-item {
            background-color: var(--vscode-panel-background);
            padding: 10px;
            border-radius: 3px;
            border: 1px solid var(--vscode-panel-border);
        }
        .content {
            background-color: var(--vscode-panel-background);
            padding: 15px;
            border-radius: 5px;
            border: 1px solid var(--vscode-panel-border);
            max-height: 70vh;
            overflow-y: auto;
            white-space: pre-wrap;
            font-size: 12px;
        }
        .user-msg { color: #4CAF50; }
        .ai-msg { color: #2196F3; }
        .timestamp { color: #888; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Auto-Observation Temps Réel</h1>
        <p>Surveillance active de la conversation pour amélioration continue</p>
    </div>
    
    <div class="stats">
        <div class="stat-item">
            <strong>📊 Total Messages:</strong><br>
            ${conversationData.totalMessages}
        </div>
        <div class="stat-item">
            <strong>⏰ Dernier Message:</strong><br>
            ${conversationData.lastMessageTime.toLocaleString()}
        </div>
        <div class="stat-item">
            <strong>👤 Dernier Utilisateur:</strong><br>
            ${conversationData.lastUserMessage.substring(0, 50)}...
        </div>
        <div class="stat-item">
            <strong>🤖 Dernière IA:</strong><br>
            ${conversationData.lastAIMessage.substring(0, 50)}...
        </div>
    </div>

    <div class="content">
${content}
    </div>

    <script>
        // Auto-scroll vers le bas
        const content = document.querySelector('.content');
        content.scrollTop = content.scrollHeight;
    </script>
</body>
</html>`;
    }
    // Enregistrer les commandes
    context.subscriptions.push(showConversationCommand, toggleCommand, forceRelaunchCommand);
    // Démarrage automatique
    if (autoStart) {
        setTimeout(() => {
            startAutoObserver();
        }, 2000); // Attendre 2 secondes après l'activation
    }
    console.log('✅ Augment Auto Observer Final - Prêt');
}
exports.activate = activate;
function deactivate() {
    console.log('🛑 Augment Auto Observer Final - Désactivé');
}
exports.deactivate = deactivate;
//# sourceMappingURL=extension.js.map