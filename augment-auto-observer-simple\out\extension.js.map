{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,yBAAyB;AACzB,6BAA6B;AAC7B,qCAAqC;AAErC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,qDAAqD,CAAC,CAAC;IAEnE,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,aAAmC,CAAC;IACxC,IAAI,WAA2C,CAAC;IAChD,IAAI,YAAY,GAAG,CAAC,CAAC;IACrB,IAAI,gBAAgB,GAAG,KAAK,CAAC;IAC7B,IAAI,iBAAiB,GAAG,CAAC,CAAC;IAC1B,IAAI,aAAyC,CAAC;IAE9C,2BAA2B;IAC3B,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACxF,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,qBAAqB;IACrB,MAAM,YAAY,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,oCAAoC,EAAE,KAAK,IAAI,EAAE;QAClG,IAAI,QAAQ,EAAE;YACV,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,8BAA8B,CAAC,CAAC;YACrE,OAAO;SACV;QAED,IAAI;YACA,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE;gBAClB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;aAC7C;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,+BAA+B,CAAC,CAAC;YAEvF,gCAAgC;YAChC,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBACzB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,uFAAuF,CAAC,CAAC;gBACxH,OAAO;aACV;YAED,6BAA6B;YAC7B,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACnC,YAAY,GAAG,KAAK,CAAC,IAAI,CAAC;YAE1B,2BAA2B;YAC3B,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE;gBAClC,UAAU,EAAE,IAAI;gBAChB,aAAa,EAAE,IAAI;aACtB,CAAC,CAAC;YAEH,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBAC1B,kBAAkB,CAAC,OAAO,CAAC,CAAC;YAChC,CAAC,CAAC,CAAC;YAEH,QAAQ,GAAG,IAAI,CAAC;YAChB,aAAa,CAAC,IAAI,GAAG,6BAA6B,CAAC;YACnD,aAAa,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;YAC3F,aAAa,CAAC,IAAI,EAAE,CAAC;YAErB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,uCAAuC,CAAC,CAAC;YAC9E,OAAO,CAAC,GAAG,CAAC,6BAA6B,EAAE,OAAO,CAAC,CAAC;SAEvD;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;SAChE;IACL,CAAC,CAAC,CAAC;IAEH,oBAAoB;IACpB,MAAM,WAAW,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,mCAAmC,EAAE,GAAG,EAAE;QAC1F,IAAI,CAAC,QAAQ,EAAE;YACX,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;YACtE,OAAO;SACV;QAED,IAAI,WAAW,EAAE;YACb,WAAW,CAAC,KAAK,EAAE,CAAC;YACpB,WAAW,GAAG,SAAS,CAAC;SAC3B;QAED,IAAI,aAAa,EAAE;YACf,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,aAAa,GAAG,SAAS,CAAC;SAC7B;QAED,QAAQ,GAAG,KAAK,CAAC;QACjB,gBAAgB,GAAG,KAAK,CAAC;QACzB,aAAa,CAAC,IAAI,GAAG,qCAAqC,CAAC;QAC3D,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;QAE1C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;IACxE,CAAC,CAAC,CAAC;IAEH,8BAA8B;IAC9B,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sCAAsC,EAAE,GAAG,EAAE;QAChG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sCAAsC,EAAE,GAAG,EAAE;QAChG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,sCAAsC,EAAE,GAAG,EAAE;QAChG,iBAAiB,CAAC,CAAC,CAAC,CAAC;IACzB,CAAC,CAAC,CAAC;IAEH,SAAS,kBAAkB,CAAC,OAAe;QACvC,IAAI;YACA,MAAM,KAAK,GAAG,EAAE,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;YACnC,MAAM,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC;YAE/B,IAAI,WAAW,GAAG,YAAY,EAAE;gBAC5B,OAAO,CAAC,GAAG,CAAC,gCAAgC,WAAW,GAAG,YAAY,QAAQ,CAAC,CAAC;gBAEhF,0BAA0B;gBAC1B,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;gBACjD,MAAM,UAAU,GAAG,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;gBAE/C,8BAA8B;gBAC9B,iBAAiB,CAAC,UAAU,CAAC,CAAC;gBAE9B,YAAY,GAAG,WAAW,CAAC;aAC9B;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,yBAAyB,EAAE,KAAK,CAAC,CAAC;SACnD;IACL,CAAC;IAED,SAAS,iBAAiB,CAAC,OAAe;QACtC,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;QAEhD,yCAAyC;QACzC,IAAI,wBAAwB,CAAC,OAAO,CAAC,EAAE;YACnC,OAAO,CAAC,GAAG,CAAC,uCAAuC,CAAC,CAAC;YAErD,gBAAgB,GAAG,IAAI,CAAC;YACxB,iBAAiB,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;YAE/B,wBAAwB;YACxB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,wCAAwC,EACxC,YAAY,EACZ,cAAc,EACd,gBAAgB,CACnB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACf,QAAQ,SAAS,EAAE;oBACf,KAAK,YAAY;wBACb,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC;wBACvE,MAAM;oBACV,KAAK,cAAc;wBACf,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC;wBACvE,MAAM;oBACV,KAAK,gBAAgB;wBACjB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,sCAAsC,CAAC,CAAC;wBACvE,MAAM;iBACb;YACL,CAAC,CAAC,CAAC;YAEH,6CAA6C;YAC7C,kBAAkB,EAAE,CAAC;SACxB;IACL,CAAC;IAED,SAAS,wBAAwB,CAAC,OAAe;QAC7C,MAAM,QAAQ,GAAG;YACb,yBAAyB;YACzB,OAAO;YACP,MAAM;YACN,OAAO;YACP,uBAAuB;SAC1B,CAAC;QAEF,MAAM,YAAY,GAAG,OAAO,CAAC,WAAW,EAAE,CAAC;QAC3C,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE;YAC5B,IAAI,YAAY,CAAC,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,EAAE;gBAC9C,YAAY,EAAE,CAAC;aAClB;SACJ;QAED,OAAO,YAAY,IAAI,CAAC,CAAC;IAC7B,CAAC;IAED,SAAS,kBAAkB;QACvB,yCAAyC;QACzC,IAAI,aAAa,EAAE;YACf,YAAY,CAAC,aAAa,CAAC,CAAC;SAC/B;QAED,OAAO,CAAC,GAAG,CAAC,0CAA0C,CAAC,CAAC;QAExD,aAAa,GAAG,UAAU,CAAC,GAAG,EAAE;YAC5B,IAAI,gBAAgB,EAAE;gBAClB,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;gBACpD,eAAe,EAAE,CAAC;aACrB;QACL,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,cAAc;IAC7B,CAAC;IAED,SAAS,iBAAiB,CAAC,MAAc;QACrC,gBAAgB,GAAG,KAAK,CAAC;QAEzB,IAAI,aAAa,EAAE;YACf,YAAY,CAAC,aAAa,CAAC,CAAC;YAC5B,aAAa,GAAG,SAAS,CAAC;SAC7B;QAED,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC,EAAE,OAAO;gBACX,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;gBAC7C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,kCAAkC,CAAC,CAAC;gBACzE,MAAM;YACV,KAAK,CAAC,EAAE,UAAU;gBACd,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAC/C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,2CAA2C,CAAC,CAAC;gBAClF,MAAM;YACV,KAAK,CAAC,EAAE,WAAW;gBACf,OAAO,CAAC,GAAG,CAAC,wCAAwC,CAAC,CAAC;gBACtD,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;gBACpE,MAAM;SACb;IACL,CAAC;IAED,SAAS,eAAe;QACpB,gBAAgB,GAAG,KAAK,CAAC;QAEzB,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC,CAAC;QAE7C,mCAAmC;QACnC,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,+CAA+C,EAC/C,WAAW,CACd,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;YACf,IAAI,SAAS,KAAK,WAAW,EAAE;gBAC3B,6CAA6C;gBAC7C,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,iEAAiE,CAAC,CAAC;aAC3G;QACL,CAAC,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI;YACA,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;SACrD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,gCAAgC,EAAE,KAAK,CAAC,CAAC;SAC1D;IACL,CAAC;IAED,mCAAmC;IACnC,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,YAAY,EACZ,WAAW,EACX,cAAc,EACd,cAAc,EACd,cAAc,CACjB,CAAC;IAEF,wBAAwB;IACxB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,oCAAoC,CAAC,CAAC;IAErE,OAAO,CAAC,GAAG,CAAC,kDAAkD,CAAC,CAAC;AACpE,CAAC;AAhQD,4BAgQC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,wDAAwD,CAAC,CAAC;AAC1E,CAAC;AAFD,gCAEC"}