{"version": 3, "file": "extension.js", "sourceRoot": "", "sources": ["../src/extension.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,yBAAyB;AACzB,6BAA6B;AAC7B,qCAAqC;AAUrC,SAAgB,QAAQ,CAAC,OAAgC;IACrD,OAAO,CAAC,GAAG,CAAC,6CAA6C,CAAC,CAAC;IAE3D,IAAI,QAAQ,GAAG,KAAK,CAAC;IACrB,IAAI,aAAmC,CAAC;IACxC,IAAI,WAA2C,CAAC;IAChD,IAAI,gBAAgB,GAAqB;QACrC,aAAa,EAAE,CAAC;QAChB,aAAa,EAAE,EAAE;QACjB,eAAe,EAAE,EAAE;QACnB,eAAe,EAAE,IAAI,IAAI,EAAE;QAC3B,mBAAmB,EAAE,EAAE;KAC1B,CAAC;IACF,IAAI,eAA2C,CAAC;IAChD,IAAI,iBAAkD,CAAC;IAEvD,gBAAgB;IAChB,MAAM,MAAM,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,CAAC,6BAA6B,CAAC,CAAC;IAChF,MAAM,iBAAiB,GAAG,MAAM,CAAC,GAAG,CAAC,mBAAmB,EAAE,EAAE,CAAC,GAAG,IAAI,CAAC,CAAC,QAAQ;IAC9E,MAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,CAAC;IACpE,MAAM,SAAS,GAAG,MAAM,CAAC,GAAG,CAAC,WAAW,EAAE,IAAI,CAAC,CAAC;IAEhD,2BAA2B;IAC3B,aAAa,GAAG,MAAM,CAAC,MAAM,CAAC,mBAAmB,CAAC,MAAM,CAAC,kBAAkB,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;IACxF,aAAa,CAAC,OAAO,GAAG,8CAA8C,CAAC;IACvE,OAAO,CAAC,aAAa,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;IAE1C,kCAAkC;IAClC,MAAM,uBAAuB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,8CAA8C,EAAE,GAAG,EAAE;QACjH,qBAAqB,EAAE,CAAC;IAC5B,CAAC,CAAC,CAAC;IAEH,oCAAoC;IACpC,MAAM,aAAa,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,gDAAgD,EAAE,GAAG,EAAE;QACzG,IAAI,QAAQ,EAAE;YACV,gBAAgB,EAAE,CAAC;SACtB;aAAM;YACH,iBAAiB,EAAE,CAAC;SACvB;IACL,CAAC,CAAC,CAAC;IAEH,2BAA2B;IAC3B,MAAM,oBAAoB,GAAG,MAAM,CAAC,QAAQ,CAAC,eAAe,CAAC,2CAA2C,EAAE,GAAG,EAAE;QAC3G,iBAAiB,CAAC,QAAQ,CAAC,CAAC;IAChC,CAAC,CAAC,CAAC;IAEH,SAAS,iBAAiB;QACtB,IAAI,QAAQ;YAAE,OAAO;QAErB,IAAI;YACA,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,CAAC,eAAe,EAAE;gBAClB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,wBAAwB,CAAC,CAAC;gBACzD,OAAO;aACV;YAED,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,+BAA+B,CAAC,CAAC;YAEvF,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE;gBACzB,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,8EAA8E,CAAC,CAAC;gBAC/G,OAAO;aACV;YAED,0BAA0B;YAC1B,oBAAoB,CAAC,OAAO,CAAC,CAAC;YAE9B,2BAA2B;YAC3B,WAAW,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,EAAE;gBAClC,UAAU,EAAE,IAAI;gBAChB,aAAa,EAAE,IAAI;aACtB,CAAC,CAAC;YAEH,WAAW,CAAC,EAAE,CAAC,QAAQ,EAAE,GAAG,EAAE;gBAC1B,oBAAoB,CAAC,OAAO,CAAC,CAAC;gBAC9B,oBAAoB,EAAE,CAAC;YAC3B,CAAC,CAAC,CAAC;YAEH,QAAQ,GAAG,IAAI,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,oBAAoB,EAAE,CAAC;YAEvB,IAAI,mBAAmB,EAAE;gBACrB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,+BAA+B,CAAC,CAAC;aACzE;YAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;SAE9C;QAAC,OAAO,KAAK,EAAE;YACZ,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,qBAAqB,KAAK,EAAE,CAAC,CAAC;SAChE;IACL,CAAC;IAED,SAAS,gBAAgB;QACrB,IAAI,CAAC,QAAQ;YAAE,OAAO;QAEtB,IAAI,WAAW,EAAE;YACb,WAAW,CAAC,KAAK,EAAE,CAAC;YACpB,WAAW,GAAG,SAAS,CAAC;SAC3B;QAED,IAAI,eAAe,EAAE;YACjB,YAAY,CAAC,eAAe,CAAC,CAAC;YAC9B,eAAe,GAAG,SAAS,CAAC;SAC/B;QAED,QAAQ,GAAG,KAAK,CAAC;QACjB,eAAe,EAAE,CAAC;QAElB,IAAI,mBAAmB,EAAE;YACrB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAAC,6BAA6B,CAAC,CAAC;SACvE;QAED,OAAO,CAAC,GAAG,CAAC,6BAA6B,CAAC,CAAC;IAC/C,CAAC;IAED,SAAS,oBAAoB,CAAC,OAAe;QACzC,IAAI;YACA,MAAM,OAAO,GAAG,EAAE,CAAC,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;YACjD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAElC,uBAAuB;YACvB,MAAM,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC,CAAC;YACtE,MAAM,aAAa,GAAG,YAAY,CAAC,MAAM,CAAC;YAE1C,gDAAgD;YAChD,IAAI,aAAa,GAAG,EAAE,CAAC;YACvB,IAAI,eAAe,GAAG,EAAE,CAAC;YACzB,IAAI,eAAe,GAAG,IAAI,IAAI,EAAE,CAAC;YAEjC,0DAA0D;YAC1D,KAAK,IAAI,CAAC,GAAG,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,EAAE;gBACxC,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC;gBAEtB,IAAI,IAAI,CAAC,QAAQ,CAAC,eAAe,CAAC,IAAI,CAAC,aAAa,EAAE;oBAClD,oCAAoC;oBACpC,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,EAAE,CAAC,EAAE,EAAE;wBACrD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;4BACnF,aAAa,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;4BAChC,MAAM;yBACT;qBACJ;iBACJ;gBAED,IAAI,IAAI,CAAC,QAAQ,CAAC,iBAAiB,CAAC,IAAI,CAAC,eAAe,EAAE;oBACtD,6CAA6C;oBAC7C,KAAK,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,EAAE,EAAE;wBACpD,IAAI,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE;4BAC/C,eAAe,GAAG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE,CAAC;4BAClC,MAAM;yBACT;qBACJ;iBACJ;gBAED,wCAAwC;gBACxC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE;oBACxE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC;oBAC/C,IAAI,cAAc,EAAE;wBAChB,IAAI;4BACA,eAAe,GAAG,IAAI,IAAI,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC,CAAC;yBACjD;wBAAC,OAAO,CAAC,EAAE;4BACR,4CAA4C;yBAC/C;qBACJ;oBACD,MAAM;iBACT;aACJ;YAED,4BAA4B;YAC5B,MAAM,aAAa,GAAG,gBAAgB,CAAC,aAAa,CAAC;YACrD,gBAAgB,GAAG;gBACf,aAAa;gBACb,aAAa;gBACb,eAAe;gBACf,eAAe;gBACf,mBAAmB,EAAE,OAAO;aAC/B,CAAC;YAEF,6BAA6B;YAC7B,IAAI,aAAa,GAAG,aAAa,EAAE;gBAC/B,OAAO,CAAC,GAAG,CAAC,kCAAkC,aAAa,GAAG,aAAa,EAAE,CAAC,CAAC;gBAE/E,mCAAmC;gBACnC,IAAI,iBAAiB,EAAE;oBACnB,uBAAuB,EAAE,CAAC;iBAC7B;aACJ;YAED,eAAe,EAAE,CAAC;SAErB;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,8BAA8B,EAAE,KAAK,CAAC,CAAC;SACxD;IACL,CAAC;IAED,SAAS,oBAAoB;QACzB,IAAI,eAAe,EAAE;YACjB,YAAY,CAAC,eAAe,CAAC,CAAC;SACjC;QAED,eAAe,GAAG,UAAU,CAAC,GAAG,EAAE;YAC9B,IAAI,QAAQ,EAAE;gBACV,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;gBAC/C,iBAAiB,CAAC,YAAY,CAAC,CAAC;aACnC;QACL,CAAC,EAAE,iBAAiB,CAAC,CAAC;IAC1B,CAAC;IAED,SAAS,oBAAoB;QACzB,IAAI,QAAQ,EAAE;YACV,oBAAoB,EAAE,CAAC;SAC1B;IACL,CAAC;IAED,SAAS,iBAAiB,CAAC,MAAc;QACrC,OAAO,CAAC,GAAG,CAAC,gCAAgC,MAAM,EAAE,CAAC,CAAC;QAEtD,sCAAsC;QACtC,IAAI,mBAAmB,EAAE;YACrB,MAAM,CAAC,MAAM,CAAC,sBAAsB,CAChC,kBAAkB,MAAM,mCAAmC,EAC3D,aAAa,EACb,mBAAmB,CACtB,CAAC,IAAI,CAAC,SAAS,CAAC,EAAE;gBACf,QAAQ,SAAS,EAAE;oBACf,KAAK,aAAa;wBACd,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;wBAClD,MAAM;oBACV,KAAK,mBAAmB;wBACpB,qBAAqB,EAAE,CAAC;wBACxB,MAAM;iBACb;YACL,CAAC,CAAC,CAAC;SACN;QAED,sCAAsC;QACtC,IAAI;YACA,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,iBAAiB,CAAC,CAAC;SACrD;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,2BAA2B,EAAE,KAAK,CAAC,CAAC;SACrD;QAED,qCAAqC;QACrC,IAAI;YACA,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;YAC/D,IAAI,eAAe,EAAE;gBACjB,MAAM,UAAU,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,wBAAwB,CAAC,CAAC;gBACnF,MAAM,aAAa,GAAG,gBAAgB,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,aAAa,MAAM,mEAAmE,CAAC;gBACrJ,EAAE,CAAC,aAAa,CAAC,UAAU,EAAE,aAAa,EAAE,MAAM,CAAC,CAAC;gBACpD,OAAO,CAAC,GAAG,CAAC,yBAAyB,EAAE,UAAU,CAAC,CAAC;aACtD;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,iCAAiC,EAAE,KAAK,CAAC,CAAC;SAC3D;QAED,sBAAsB;QACtB,oBAAoB,EAAE,CAAC;IAC3B,CAAC;IAED,SAAS,eAAe;QACpB,IAAI,QAAQ,EAAE;YACV,aAAa,CAAC,IAAI,GAAG,oBAAoB,gBAAgB,CAAC,aAAa,OAAO,CAAC;YAC/E,aAAa,CAAC,eAAe,GAAG,IAAI,MAAM,CAAC,UAAU,CAAC,mCAAmC,CAAC,CAAC;YAC3F,aAAa,CAAC,OAAO,GAAG,+CAA+C,gBAAgB,CAAC,aAAa,cAAc,gBAAgB,CAAC,eAAe,CAAC,kBAAkB,EAAE,EAAE,CAAC;SAC9K;aAAM;YACH,aAAa,CAAC,IAAI,GAAG,6BAA6B,CAAC;YACnD,aAAa,CAAC,eAAe,GAAG,SAAS,CAAC;YAC1C,aAAa,CAAC,OAAO,GAAG,0BAA0B,CAAC;SACtD;QACD,aAAa,CAAC,IAAI,EAAE,CAAC;IACzB,CAAC;IAED,SAAS,qBAAqB;QAC1B,IAAI,iBAAiB,EAAE;YACnB,iBAAiB,CAAC,MAAM,EAAE,CAAC;YAC3B,OAAO;SACV;QAED,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAChD,qBAAqB,EACrB,oCAAoC,EACpC,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,iBAAiB,CAAC,YAAY,CAAC,GAAG,EAAE;YAChC,iBAAiB,GAAG,SAAS,CAAC;QAClC,CAAC,CAAC,CAAC;QAEH,uBAAuB,EAAE,CAAC;IAC9B,CAAC;IAED,SAAS,uBAAuB;QAC5B,IAAI,CAAC,iBAAiB;YAAE,OAAO;QAE/B,MAAM,IAAI,GAAG,wBAAwB,EAAE,CAAC;QACxC,iBAAiB,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC;IAC1C,CAAC;IAED,SAAS,wBAAwB;QAC7B,MAAM,OAAO,GAAG,gBAAgB,CAAC,mBAAmB;aAC/C,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,IAAI,EAAE,MAAM,CAAC;aACrB,OAAO,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAE5B,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;cAyDD,gBAAgB,CAAC,aAAa;;;;cAI9B,gBAAgB,CAAC,eAAe,CAAC,cAAc,EAAE;;;;cAIjD,gBAAgB,CAAC,eAAe,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;;;cAIjD,gBAAgB,CAAC,aAAa,CAAC,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;;;;;EAK3D,OAAO;;;;;;;;;QASD,CAAC;IACL,CAAC;IAED,4BAA4B;IAC5B,OAAO,CAAC,aAAa,CAAC,IAAI,CACtB,uBAAuB,EACvB,aAAa,EACb,oBAAoB,CACvB,CAAC;IAEF,wBAAwB;IACxB,IAAI,SAAS,EAAE;QACX,UAAU,CAAC,GAAG,EAAE;YACZ,iBAAiB,EAAE,CAAC;QACxB,CAAC,EAAE,IAAI,CAAC,CAAC,CAAC,yCAAyC;KACtD;IAED,OAAO,CAAC,GAAG,CAAC,sCAAsC,CAAC,CAAC;AACxD,CAAC;AAxZD,4BAwZC;AAED,SAAgB,UAAU;IACtB,OAAO,CAAC,GAAG,CAAC,4CAA4C,CAAC,CAAC;AAC9D,CAAC;AAFD,gCAEC"}