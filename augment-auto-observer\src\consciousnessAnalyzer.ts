import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import { ConversationMessage } from './conversationMonitor';

export interface ConsciousnessState {
    phase: string;
    objectives: string[];
    userSatisfaction: number;
    aiPerformance: number;
    understandingLevel: number;
    alignmentLevel: number;
    correctionsCount: number;
    lastCorrectionType: string;
}

export interface ValidationChoice {
    recommendedAction: 'stop' | 'confirm' | 'continue';
    confidenceLevel: number;
    stopReason: string;
    confirmReason: string;
    continueReason: string;
}

export class ConsciousnessAnalyzer {
    private context: vscode.ExtensionContext;
    private isActive = false;
    private currentState: ConsciousnessState;
    private analysisFile?: string;

    constructor(context: vscode.ExtensionContext) {
        this.context = context;
        this.currentState = {
            phase: 'initialization',
            objectives: [],
            userSatisfaction: 0.5,
            aiPerformance: 0.5,
            understandingLevel: 0.5,
            alignmentLevel: 0.5,
            correctionsCount: 0,
            lastCorrectionType: ''
        };
    }

    async start(): Promise<void> {
        if (this.isActive) {
            return;
        }

        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            throw new Error('Aucun workspace ouvert');
        }

        this.analysisFile = path.join(workspaceFolder.uri.fsPath, 'consciousness_analysis.json');
        this.isActive = true;

        console.log('🧠 ConsciousnessAnalyzer démarré');
    }

    stop(): void {
        this.isActive = false;
        console.log('🛑 ConsciousnessAnalyzer arrêté');
    }

    analyzeMessages(messages: ConversationMessage[]): void {
        if (!this.isActive || messages.length === 0) {
            return;
        }

        try {
            // Analyser l'évolution de la conversation
            this.analyzeConversationEvolution(messages);
            
            // Évaluer la satisfaction utilisateur
            this.evaluateUserSatisfaction(messages);
            
            // Générer le choix de validation
            const validationChoice = this.generateValidationChoice();
            
            // Sauvegarder l'analyse
            this.saveAnalysis(validationChoice);
            
            // Afficher le statut
            this.displayStatus(validationChoice);

        } catch (error) {
            console.error('Erreur analyse conscience:', error);
        }
    }

    private analyzeConversationEvolution(messages: ConversationMessage[]): void {
        // Détecter la phase de conversation
        this.currentState.phase = this.detectConversationPhase(messages);
        
        // Extraire les objectifs
        this.currentState.objectives = this.extractObjectives(messages);
        
        // Évaluer la performance IA
        this.currentState.aiPerformance = this.evaluateAIPerformance(messages);
    }

    private detectConversationPhase(messages: ConversationMessage[]): string {
        if (messages.length === 0) {
            return 'initialization';
        }

        const recentMessages = messages.slice(-10);
        let correctionCount = 0;

        const correctionPatterns = [
            'tu t\'arrêtes', 'pourquoi', 'tu n\'exploites pas',
            'il faudrait', 'tu dois', 'normalement', 'non', 'ce n\'est pas'
        ];

        for (const msg of recentMessages) {
            const content = msg.userMessage.toLowerCase();
            for (const pattern of correctionPatterns) {
                if (content.includes(pattern)) {
                    correctionCount++;
                    break;
                }
            }
        }

        this.currentState.correctionsCount = correctionCount;

        if (correctionCount >= 3) {
            return 'correction_intensive';
        } else if (correctionCount >= 1) {
            return 'guidance';
        } else {
            return 'collaboration';
        }
    }

    private extractObjectives(messages: ConversationMessage[]): string[] {
        const objectives: string[] = [];
        
        const objectivePatterns = [
            /il faut(?:rait)?\s+(.+)/gi,
            /nous (?:devons|allons)\s+(.+)/gi,
            /l'objectif (?:est|c'est)\s+(.+)/gi,
            /le but (?:est|c'est)\s+(.+)/gi
        ];

        // Analyser les 20 derniers messages
        const recentMessages = messages.slice(-20);
        
        for (const msg of recentMessages) {
            const content = msg.userMessage.toLowerCase();
            
            for (const pattern of objectivePatterns) {
                const matches = content.match(pattern);
                if (matches) {
                    objectives.push(...matches);
                }
            }
        }

        return [...new Set(objectives)]; // Supprimer doublons
    }

    private evaluateAIPerformance(messages: ConversationMessage[]): number {
        if (messages.length < 4) {
            return 0.5;
        }

        let userCorrections = 0;
        let aiResponses = 0;

        // Analyser les 20 derniers messages
        const recentMessages = messages.slice(-20);

        for (const msg of recentMessages) {
            if (msg.userMessage) {
                const content = msg.userMessage.toLowerCase();
                
                // Détecter patterns de correction
                const correctionPatterns = [
                    'tu t\'arrêtes', 'pourquoi', 'tu n\'exploites pas',
                    'il faudrait', 'tu dois', 'normalement', 'non'
                ];
                
                if (correctionPatterns.some(pattern => content.includes(pattern))) {
                    userCorrections++;
                }
            }
            
            if (msg.assistantResponse) {
                aiResponses++;
            }
        }

        if (aiResponses === 0) {
            return 0.5;
        }

        // Performance inversement proportionnelle aux corrections
        const correctionRatio = userCorrections / aiResponses;
        const performance = Math.max(0.1, 1.0 - correctionRatio);

        return Math.min(performance, 1.0);
    }

    private evaluateUserSatisfaction(messages: ConversationMessage[]): void {
        if (messages.length === 0) {
            return;
        }

        const positivePatterns = [
            'parfait', 'excellent', 'très bien', 'c\'est exactement ça',
            'oui', 'correct', 'bien', 'super', 'génial'
        ];

        const negativePatterns = [
            'non', 'tu t\'arrêtes', 'pourquoi', 'tu n\'exploites pas',
            'il faudrait', 'tu dois', 'pas vraiment', 'je n\'ai pas l\'impression'
        ];

        const recentMessages = messages.slice(-10);
        let positiveScore = 0;
        let negativeScore = 0;

        for (const msg of recentMessages) {
            const content = msg.userMessage.toLowerCase();
            
            for (const pattern of positivePatterns) {
                if (content.includes(pattern)) {
                    positiveScore++;
                    break;
                }
            }
            
            for (const pattern of negativePatterns) {
                if (content.includes(pattern)) {
                    negativeScore++;
                    break;
                }
            }
        }

        const totalSignals = positiveScore + negativeScore;
        if (totalSignals > 0) {
            this.currentState.userSatisfaction = positiveScore / totalSignals;
        }

        // Calculer niveau de compréhension et alignement
        this.currentState.understandingLevel = Math.max(0.1, 1.0 - (negativeScore / Math.max(recentMessages.length, 1)));
        this.currentState.alignmentLevel = this.currentState.userSatisfaction;
    }

    private generateValidationChoice(): ValidationChoice {
        const understanding = this.currentState.understandingLevel;
        const alignment = this.currentState.alignmentLevel;
        const performance = this.currentState.aiPerformance;
        const satisfaction = this.currentState.userSatisfaction;

        // Calcul de confiance globale
        const confidence = (understanding + alignment + performance + satisfaction) / 4;

        const choice: ValidationChoice = {
            recommendedAction: 'continue',
            confidenceLevel: confidence,
            stopReason: '',
            confirmReason: '',
            continueReason: ''
        };

        // Logique de décision
        if (confidence < 0.6 || this.currentState.correctionsCount >= 3) {
            choice.recommendedAction = 'stop';
            choice.stopReason = this.generateStopReason();
        } else if (confidence < 0.8 || this.currentState.phase === 'guidance') {
            choice.recommendedAction = 'confirm';
            choice.confirmReason = this.generateConfirmReason();
        } else {
            choice.recommendedAction = 'continue';
            choice.continueReason = this.generateContinueReason();
        }

        return choice;
    }

    private generateStopReason(): string {
        const reasons: string[] = [];
        
        if (this.currentState.understandingLevel < 0.6) {
            reasons.push('compréhension insuffisante');
        }
        if (this.currentState.correctionsCount >= 3) {
            reasons.push('trop de corrections reçues');
        }
        if (this.currentState.userSatisfaction < 0.4) {
            reasons.push('satisfaction utilisateur faible');
        }

        return reasons.length > 0 ? reasons.join(' | ') : 'besoin de clarification';
    }

    private generateConfirmReason(): string {
        const reasons: string[] = [];
        
        if (this.currentState.understandingLevel >= 0.6 && this.currentState.understandingLevel < 0.8) {
            reasons.push('vérifier ma compréhension');
        }
        if (this.currentState.phase === 'guidance') {
            reasons.push('phase de guidance active');
        }

        return reasons.length > 0 ? reasons.join(' | ') : 'validation de l\'alignement';
    }

    private generateContinueReason(): string {
        const reasons: string[] = [];
        
        if (this.currentState.understandingLevel >= 0.8) {
            reasons.push('bonne compréhension');
        }
        if (this.currentState.userSatisfaction >= 0.7) {
            reasons.push('satisfaction utilisateur élevée');
        }

        return reasons.length > 0 ? reasons.join(' | ') : 'alignement optimal';
    }

    private saveAnalysis(validationChoice: ValidationChoice): void {
        if (!this.analysisFile) return;

        const analysisData = {
            timestamp: new Date().toISOString(),
            consciousness_state: this.currentState,
            validation_choice: validationChoice
        };

        try {
            fs.writeFileSync(this.analysisFile, JSON.stringify(analysisData, null, 2), 'utf8');
        } catch (error) {
            console.error('Erreur sauvegarde analyse:', error);
        }
    }

    private displayStatus(validationChoice: ValidationChoice): void {
        const status = `🧠 CONSCIENCE: ${this.currentState.phase} | ` +
                      `😊 Satisfaction: ${this.currentState.userSatisfaction.toFixed(2)} | ` +
                      `🤖 Performance: ${this.currentState.aiPerformance.toFixed(2)} | ` +
                      `🔄 Recommandation: ${validationChoice.recommendedAction.toUpperCase()}`;

        console.log(status);

        // Afficher notification si nécessaire
        if (validationChoice.recommendedAction === 'stop') {
            vscode.window.showWarningMessage(`🛑 IA recommande STOP: ${validationChoice.stopReason}`);
        }
    }

    getCurrentState(): ConsciousnessState {
        return { ...this.currentState };
    }

    getLastValidationChoice(): ValidationChoice | null {
        if (!this.analysisFile || !fs.existsSync(this.analysisFile)) {
            return null;
        }

        try {
            const data = JSON.parse(fs.readFileSync(this.analysisFile, 'utf8'));
            return data.validation_choice;
        } catch (error) {
            return null;
        }
    }
}
