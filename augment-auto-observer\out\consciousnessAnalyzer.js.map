{"version": 3, "file": "consciousnessAnalyzer.js", "sourceRoot": "", "sources": ["../src/consciousnessAnalyzer.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AACjC,yBAAyB;AACzB,6BAA6B;AAsB7B,MAAa,qBAAqB;IAM9B,YAAY,OAAgC;QAJpC,aAAQ,GAAG,KAAK,CAAC;QAKrB,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,YAAY,GAAG;YAChB,KAAK,EAAE,gBAAgB;YACvB,UAAU,EAAE,EAAE;YACd,gBAAgB,EAAE,GAAG;YACrB,aAAa,EAAE,GAAG;YAClB,kBAAkB,EAAE,GAAG;YACvB,cAAc,EAAE,GAAG;YACnB,gBAAgB,EAAE,CAAC;YACnB,kBAAkB,EAAE,EAAE;SACzB,CAAC;IACN,CAAC;IAED,KAAK,CAAC,KAAK;QACP,IAAI,IAAI,CAAC,QAAQ,EAAE;YACf,OAAO;SACV;QAED,MAAM,eAAe,GAAG,MAAM,CAAC,SAAS,CAAC,gBAAgB,EAAE,CAAC,CAAC,CAAC,CAAC;QAC/D,IAAI,CAAC,eAAe,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,wBAAwB,CAAC,CAAC;SAC7C;QAED,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,MAAM,EAAE,6BAA6B,CAAC,CAAC;QACzF,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC;QAErB,OAAO,CAAC,GAAG,CAAC,kCAAkC,CAAC,CAAC;IACpD,CAAC;IAED,IAAI;QACA,IAAI,CAAC,QAAQ,GAAG,KAAK,CAAC;QACtB,OAAO,CAAC,GAAG,CAAC,iCAAiC,CAAC,CAAC;IACnD,CAAC;IAED,eAAe,CAAC,QAA+B;QAC3C,IAAI,CAAC,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACzC,OAAO;SACV;QAED,IAAI;YACA,0CAA0C;YAC1C,IAAI,CAAC,4BAA4B,CAAC,QAAQ,CAAC,CAAC;YAE5C,sCAAsC;YACtC,IAAI,CAAC,wBAAwB,CAAC,QAAQ,CAAC,CAAC;YAExC,iCAAiC;YACjC,MAAM,gBAAgB,GAAG,IAAI,CAAC,wBAAwB,EAAE,CAAC;YAEzD,wBAAwB;YACxB,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,CAAC;YAEpC,qBAAqB;YACrB,IAAI,CAAC,aAAa,CAAC,gBAAgB,CAAC,CAAC;SAExC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;SACtD;IACL,CAAC;IAEO,4BAA4B,CAAC,QAA+B;QAChE,oCAAoC;QACpC,IAAI,CAAC,YAAY,CAAC,KAAK,GAAG,IAAI,CAAC,uBAAuB,CAAC,QAAQ,CAAC,CAAC;QAEjE,yBAAyB;QACzB,IAAI,CAAC,YAAY,CAAC,UAAU,GAAG,IAAI,CAAC,iBAAiB,CAAC,QAAQ,CAAC,CAAC;QAEhE,4BAA4B;QAC5B,IAAI,CAAC,YAAY,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,CAAC,QAAQ,CAAC,CAAC;IAC3E,CAAC;IAEO,uBAAuB,CAAC,QAA+B;QAC3D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO,gBAAgB,CAAC;SAC3B;QAED,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3C,IAAI,eAAe,GAAG,CAAC,CAAC;QAExB,MAAM,kBAAkB,GAAG;YACvB,eAAe,EAAE,UAAU,EAAE,qBAAqB;YAClD,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK,EAAE,eAAe;SAClE,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAC9C,KAAK,MAAM,OAAO,IAAI,kBAAkB,EAAE;gBACtC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC3B,eAAe,EAAE,CAAC;oBAClB,MAAM;iBACT;aACJ;SACJ;QAED,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,eAAe,CAAC;QAErD,IAAI,eAAe,IAAI,CAAC,EAAE;YACtB,OAAO,sBAAsB,CAAC;SACjC;aAAM,IAAI,eAAe,IAAI,CAAC,EAAE;YAC7B,OAAO,UAAU,CAAC;SACrB;aAAM;YACH,OAAO,eAAe,CAAC;SAC1B;IACL,CAAC;IAEO,iBAAiB,CAAC,QAA+B;QACrD,MAAM,UAAU,GAAa,EAAE,CAAC;QAEhC,MAAM,iBAAiB,GAAG;YACtB,2BAA2B;YAC3B,iCAAiC;YACjC,mCAAmC;YACnC,+BAA+B;SAClC,CAAC;QAEF,oCAAoC;QACpC,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAE9C,KAAK,MAAM,OAAO,IAAI,iBAAiB,EAAE;gBACrC,MAAM,OAAO,GAAG,OAAO,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;gBACvC,IAAI,OAAO,EAAE;oBACT,UAAU,CAAC,IAAI,CAAC,GAAG,OAAO,CAAC,CAAC;iBAC/B;aACJ;SACJ;QAED,OAAO,CAAC,GAAG,IAAI,GAAG,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,qBAAqB;IAC1D,CAAC;IAEO,qBAAqB,CAAC,QAA+B;QACzD,IAAI,QAAQ,CAAC,MAAM,GAAG,CAAC,EAAE;YACrB,OAAO,GAAG,CAAC;SACd;QAED,IAAI,eAAe,GAAG,CAAC,CAAC;QACxB,IAAI,WAAW,GAAG,CAAC,CAAC;QAEpB,oCAAoC;QACpC,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAE3C,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;YAC9B,IAAI,GAAG,CAAC,WAAW,EAAE;gBACjB,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;gBAE9C,kCAAkC;gBAClC,MAAM,kBAAkB,GAAG;oBACvB,eAAe,EAAE,UAAU,EAAE,qBAAqB;oBAClD,aAAa,EAAE,SAAS,EAAE,aAAa,EAAE,KAAK;iBACjD,CAAC;gBAEF,IAAI,kBAAkB,CAAC,IAAI,CAAC,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC,EAAE;oBAC/D,eAAe,EAAE,CAAC;iBACrB;aACJ;YAED,IAAI,GAAG,CAAC,iBAAiB,EAAE;gBACvB,WAAW,EAAE,CAAC;aACjB;SACJ;QAED,IAAI,WAAW,KAAK,CAAC,EAAE;YACnB,OAAO,GAAG,CAAC;SACd;QAED,0DAA0D;QAC1D,MAAM,eAAe,GAAG,eAAe,GAAG,WAAW,CAAC;QACtD,MAAM,WAAW,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,eAAe,CAAC,CAAC;QAEzD,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC;IACtC,CAAC;IAEO,wBAAwB,CAAC,QAA+B;QAC5D,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;YACvB,OAAO;SACV;QAED,MAAM,gBAAgB,GAAG;YACrB,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,sBAAsB;YAC3D,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,QAAQ;SAC9C,CAAC;QAEF,MAAM,gBAAgB,GAAG;YACrB,KAAK,EAAE,eAAe,EAAE,UAAU,EAAE,qBAAqB;YACzD,aAAa,EAAE,SAAS,EAAE,cAAc,EAAE,4BAA4B;SACzE,CAAC;QAEF,MAAM,cAAc,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;QAC3C,IAAI,aAAa,GAAG,CAAC,CAAC;QACtB,IAAI,aAAa,GAAG,CAAC,CAAC;QAEtB,KAAK,MAAM,GAAG,IAAI,cAAc,EAAE;YAC9B,MAAM,OAAO,GAAG,GAAG,CAAC,WAAW,CAAC,WAAW,EAAE,CAAC;YAE9C,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE;gBACpC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC3B,aAAa,EAAE,CAAC;oBAChB,MAAM;iBACT;aACJ;YAED,KAAK,MAAM,OAAO,IAAI,gBAAgB,EAAE;gBACpC,IAAI,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;oBAC3B,aAAa,EAAE,CAAC;oBAChB,MAAM;iBACT;aACJ;SACJ;QAED,MAAM,YAAY,GAAG,aAAa,GAAG,aAAa,CAAC;QACnD,IAAI,YAAY,GAAG,CAAC,EAAE;YAClB,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,aAAa,GAAG,YAAY,CAAC;SACrE;QAED,iDAAiD;QACjD,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,IAAI,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,GAAG,CAAC,aAAa,GAAG,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;QACjH,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC;IAC1E,CAAC;IAEO,wBAAwB;QAC5B,MAAM,aAAa,GAAG,IAAI,CAAC,YAAY,CAAC,kBAAkB,CAAC;QAC3D,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,cAAc,CAAC;QACnD,MAAM,WAAW,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC;QACpD,MAAM,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC;QAExD,8BAA8B;QAC9B,MAAM,UAAU,GAAG,CAAC,aAAa,GAAG,SAAS,GAAG,WAAW,GAAG,YAAY,CAAC,GAAG,CAAC,CAAC;QAEhF,MAAM,MAAM,GAAqB;YAC7B,iBAAiB,EAAE,UAAU;YAC7B,eAAe,EAAE,UAAU;YAC3B,UAAU,EAAE,EAAE;YACd,aAAa,EAAE,EAAE;YACjB,cAAc,EAAE,EAAE;SACrB,CAAC;QAEF,sBAAsB;QACtB,IAAI,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,CAAC,EAAE;YAC7D,MAAM,CAAC,iBAAiB,GAAG,MAAM,CAAC;YAClC,MAAM,CAAC,UAAU,GAAG,IAAI,CAAC,kBAAkB,EAAE,CAAC;SACjD;aAAM,IAAI,UAAU,GAAG,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,UAAU,EAAE;YACnE,MAAM,CAAC,iBAAiB,GAAG,SAAS,CAAC;YACrC,MAAM,CAAC,aAAa,GAAG,IAAI,CAAC,qBAAqB,EAAE,CAAC;SACvD;aAAM;YACH,MAAM,CAAC,iBAAiB,GAAG,UAAU,CAAC;YACtC,MAAM,CAAC,cAAc,GAAG,IAAI,CAAC,sBAAsB,EAAE,CAAC;SACzD;QAED,OAAO,MAAM,CAAC;IAClB,CAAC;IAEO,kBAAkB;QACtB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,GAAG,EAAE;YAC5C,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,CAAC,EAAE;YACzC,OAAO,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;SAC9C;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,GAAG,GAAG,EAAE;YAC1C,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;SACnD;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,yBAAyB,CAAC;IAChF,CAAC;IAEO,qBAAqB;QACzB,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,IAAI,GAAG,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,GAAG,GAAG,EAAE;YAC3F,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;SAC7C;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK,UAAU,EAAE;YACxC,OAAO,CAAC,IAAI,CAAC,0BAA0B,CAAC,CAAC;SAC5C;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,6BAA6B,CAAC;IACpF,CAAC;IAEO,sBAAsB;QAC1B,MAAM,OAAO,GAAa,EAAE,CAAC;QAE7B,IAAI,IAAI,CAAC,YAAY,CAAC,kBAAkB,IAAI,GAAG,EAAE;YAC7C,OAAO,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;SACvC;QACD,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,IAAI,GAAG,EAAE;YAC3C,OAAO,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;SACnD;QAED,OAAO,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,oBAAoB,CAAC;IAC3E,CAAC;IAEO,YAAY,CAAC,gBAAkC;QACnD,IAAI,CAAC,IAAI,CAAC,YAAY;YAAE,OAAO;QAE/B,MAAM,YAAY,GAAG;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,mBAAmB,EAAE,IAAI,CAAC,YAAY;YACtC,iBAAiB,EAAE,gBAAgB;SACtC,CAAC;QAEF,IAAI;YACA,EAAE,CAAC,aAAa,CAAC,IAAI,CAAC,YAAY,EAAE,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC;SACtF;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,4BAA4B,EAAE,KAAK,CAAC,CAAC;SACtD;IACL,CAAC;IAEO,aAAa,CAAC,gBAAkC;QACpD,MAAM,MAAM,GAAG,kBAAkB,IAAI,CAAC,YAAY,CAAC,KAAK,KAAK;YAC/C,oBAAoB,IAAI,CAAC,YAAY,CAAC,gBAAgB,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;YACtE,mBAAmB,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,OAAO,CAAC,CAAC,CAAC,KAAK;YAClE,sBAAsB,gBAAgB,CAAC,iBAAiB,CAAC,WAAW,EAAE,EAAE,CAAC;QAEvF,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAEpB,sCAAsC;QACtC,IAAI,gBAAgB,CAAC,iBAAiB,KAAK,MAAM,EAAE;YAC/C,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,0BAA0B,gBAAgB,CAAC,UAAU,EAAE,CAAC,CAAC;SAC7F;IACL,CAAC;IAED,eAAe;QACX,OAAO,EAAE,GAAG,IAAI,CAAC,YAAY,EAAE,CAAC;IACpC,CAAC;IAED,uBAAuB;QACnB,IAAI,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI,CAAC,YAAY,CAAC,EAAE;YACzD,OAAO,IAAI,CAAC;SACf;QAED,IAAI;YACA,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,YAAY,CAAC,IAAI,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC,CAAC;YACpE,OAAO,IAAI,CAAC,iBAAiB,CAAC;SACjC;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,IAAI,CAAC;SACf;IACL,CAAC;CACJ;AA5VD,sDA4VC"}