"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConversationMonitor = void 0;
const vscode = require("vscode");
const fs = require("fs");
const path = require("path");
const chokidar = require("chokidar");
class ConversationMonitor {
    constructor(context) {
        this.isActive = false;
        this.lastMessageCount = 0;
        this.context = context;
    }
    async start() {
        if (this.isActive) {
            return;
        }
        try {
            // Trouver le fichier state.vscdb
            this.stateFile = await this.findStateFile();
            if (!this.stateFile) {
                throw new Error('Fichier state.vscdb non trouvé');
            }
            // Créer le fichier de log
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error('Aucun workspace ouvert');
            }
            this.logFile = path.join(workspaceFolder.uri.fsPath, 'augment_conversation_auto.txt');
            await this.initLogFile();
            // Obtenir l'état initial
            await this.getInitialState();
            // Démarrer la surveillance
            this.startWatching();
            this.isActive = true;
            console.log('📊 ConversationMonitor démarré');
        }
        catch (error) {
            throw new Error(`Erreur démarrage ConversationMonitor: ${error}`);
        }
    }
    stop() {
        if (!this.isActive) {
            return;
        }
        if (this.watcher) {
            this.watcher.close();
            this.watcher = undefined;
        }
        this.isActive = false;
        console.log('🛑 ConversationMonitor arrêté');
    }
    onNewMessages(callback) {
        this.onNewMessagesCallback = callback;
    }
    async findStateFile() {
        const basePath = path.join(process.env.APPDATA || '', 'Code/User/workspaceStorage');
        if (!fs.existsSync(basePath)) {
            return undefined;
        }
        const workspaceDirs = fs.readdirSync(basePath);
        const stateFiles = [];
        for (const dir of workspaceDirs) {
            const stateFile = path.join(basePath, dir, 'state.vscdb');
            if (fs.existsSync(stateFile)) {
                const stats = fs.statSync(stateFile);
                stateFiles.push({
                    file: stateFile,
                    mtime: stats.mtime.getTime()
                });
            }
        }
        if (stateFiles.length === 0) {
            return undefined;
        }
        // Trier par date de modification (plus récent en premier)
        stateFiles.sort((a, b) => b.mtime - a.mtime);
        // Prendre le plus récent qui contient des conversations Augment
        for (const { file } of stateFiles) {
            if (await this.hasAugmentConversations(file)) {
                return file;
            }
        }
        return stateFiles[0].file;
    }
    async hasAugmentConversations(stateFile) {
        return new Promise((resolve) => {
            try {
                // Import dynamique de sqlite3
                const sqlite3 = require('sqlite3');
                const db = new sqlite3.Database(stateFile, sqlite3.OPEN_READONLY, (err) => {
                    if (err) {
                        resolve(false);
                        return;
                    }
                    db.get("SELECT key FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'", (err, row) => {
                        db.close();
                        resolve(!!row);
                    });
                });
            }
            catch (error) {
                resolve(false);
            }
        });
    }
    async initLogFile() {
        if (!this.logFile)
            return;
        const timestamp = new Date().toISOString();
        const header = `AUGMENT CONVERSATION - LOG AUTOMATIQUE (Extension VSCode)
Démarré: ${timestamp}
Workspace: ${vscode.workspace.workspaceFolders?.[0]?.uri.fsPath}
Source: ${this.stateFile}
${'='.repeat(80)}

`;
        fs.writeFileSync(this.logFile, header, 'utf8');
    }
    async getInitialState() {
        try {
            const conversationData = await this.extractConversationData();
            if (conversationData) {
                const conversations = conversationData.conversations || {};
                const currentConvId = conversationData.currentConversationId;
                if (currentConvId && conversations[currentConvId]) {
                    const chatHistory = conversations[currentConvId].chatHistory || [];
                    this.lastMessageCount = chatHistory.length;
                    console.log(`📊 État initial: ${this.lastMessageCount} messages`);
                }
            }
        }
        catch (error) {
            console.error('Erreur état initial:', error);
        }
    }
    startWatching() {
        if (!this.stateFile)
            return;
        this.watcher = chokidar.watch(this.stateFile, {
            persistent: true,
            ignoreInitial: true
        });
        this.watcher.on('change', () => {
            this.processFileChange();
        });
        console.log(`👁️ Surveillance démarrée: ${this.stateFile}`);
    }
    async processFileChange() {
        try {
            // Attendre un peu pour éviter les lectures partielles
            await new Promise(resolve => setTimeout(resolve, 100));
            const conversationData = await this.extractConversationData();
            if (!conversationData)
                return;
            const conversations = conversationData.conversations || {};
            const currentConvId = conversationData.currentConversationId;
            if (!currentConvId || !conversations[currentConvId])
                return;
            const chatHistory = conversations[currentConvId].chatHistory || [];
            if (chatHistory.length > this.lastMessageCount) {
                const newMessagesRaw = chatHistory.slice(this.lastMessageCount);
                const newMessages = [];
                for (let i = 0; i < newMessagesRaw.length; i++) {
                    const msgData = newMessagesRaw[i];
                    const formattedMsg = this.formatMessage(msgData, this.lastMessageCount + i + 1);
                    newMessages.push(formattedMsg);
                }
                // Écrire dans le log
                await this.appendToLog(newMessages);
                // Notifier les nouveaux messages
                if (this.onNewMessagesCallback) {
                    this.onNewMessagesCallback(newMessages);
                }
                this.lastMessageCount = chatHistory.length;
                console.log(`✅ ${newMessages.length} nouveaux messages traités`);
            }
        }
        catch (error) {
            console.error('Erreur traitement changement:', error);
        }
    }
    async extractConversationData() {
        if (!this.stateFile)
            return null;
        return new Promise((resolve) => {
            try {
                // Import dynamique de sqlite3
                const sqlite3 = require('sqlite3');
                const db = new sqlite3.Database(this.stateFile, sqlite3.OPEN_READONLY, (err) => {
                    if (err) {
                        resolve(null);
                        return;
                    }
                    db.get("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'", (err, row) => {
                        db.close();
                        if (err || !row) {
                            resolve(null);
                            return;
                        }
                        try {
                            const value = typeof row.value === 'string' ? row.value : row.value.toString();
                            const mainData = JSON.parse(value);
                            const webviewData = JSON.parse(mainData.webviewState);
                            resolve(webviewData);
                        }
                        catch (parseError) {
                            resolve(null);
                        }
                    });
                });
            }
            catch (error) {
                resolve(null);
            }
        });
    }
    formatMessage(messageData, index) {
        const timestamp = new Date().toISOString();
        // Extraire contenu utilisateur
        const userContent = messageData.request_message || '';
        // Extraire réponse assistant
        let assistantContent = '';
        const toolsUsed = [];
        if (messageData.structured_output_nodes) {
            for (const node of messageData.structured_output_nodes) {
                if (node.type === 0 && node.content) {
                    assistantContent = node.content;
                }
                else if (node.type === 5 && node.tool_use) {
                    toolsUsed.push(node.tool_use.tool_name || 'unknown');
                }
            }
        }
        return {
            index,
            timestamp,
            userMessage: userContent,
            assistantResponse: assistantContent,
            toolsUsed,
            requestId: messageData.request_id || ''
        };
    }
    async appendToLog(messages) {
        if (!this.logFile)
            return;
        let logContent = '';
        for (const msg of messages) {
            logContent += `[${msg.timestamp}] MESSAGE #${msg.index}\n`;
            logContent += '-'.repeat(60) + '\n';
            if (msg.userMessage) {
                logContent += `👤 UTILISATEUR:\n${msg.userMessage}\n\n`;
            }
            if (msg.assistantResponse) {
                logContent += `🤖 ASSISTANT:\n${msg.assistantResponse}\n\n`;
            }
            if (msg.toolsUsed.length > 0) {
                logContent += `🔧 OUTILS: ${msg.toolsUsed.join(', ')}\n\n`;
            }
            logContent += '='.repeat(80) + '\n\n';
        }
        fs.appendFileSync(this.logFile, logContent, 'utf8');
    }
}
exports.ConversationMonitor = ConversationMonitor;
//# sourceMappingURL=conversationMonitor.js.map