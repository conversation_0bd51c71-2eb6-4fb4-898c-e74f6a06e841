"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.StatusPanel = void 0;
const vscode = require("vscode");
class StatusPanel {
    constructor(context) {
        this.context = context;
    }
    show() {
        if (this.panel) {
            this.panel.reveal();
            return;
        }
        this.panel = vscode.window.createWebviewPanel('augmentObserverStatus', 'Augment Auto Observer - Statut', vscode.ViewColumn.Beside, {
            enableScripts: true,
            retainContextWhenHidden: true
        });
        this.panel.webview.html = this.getWebviewContent();
        // Gérer la fermeture du panel
        this.panel.onDidDispose(() => {
            this.panel = undefined;
        });
        // Gérer les messages du webview
        this.panel.webview.onDidReceiveMessage(message => {
            switch (message.command) {
                case 'choice1':
                    vscode.commands.executeCommand('augment-auto-observer.choice1');
                    break;
                case 'choice2':
                    vscode.commands.executeCommand('augment-auto-observer.choice2');
                    break;
                case 'choice3':
                    vscode.commands.executeCommand('augment-auto-observer.choice3');
                    break;
                case 'forceRelaunch':
                    vscode.commands.executeCommand('augment-auto-observer.forceRelaunch');
                    break;
                case 'refresh':
                    this.updateContent();
                    break;
            }
        });
        // Mettre à jour le contenu toutes les 2 secondes
        const updateInterval = setInterval(() => {
            if (this.panel) {
                this.updateContent();
            }
            else {
                clearInterval(updateInterval);
            }
        }, 2000);
    }
    updateContent() {
        if (!this.panel) {
            return;
        }
        this.panel.webview.html = this.getWebviewContent();
    }
    getWebviewContent() {
        // Récupérer les données de statut (simulées pour l'instant)
        const status = this.getStatusData();
        return `<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Augment Auto Observer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: var(--vscode-editor-background);
            color: var(--vscode-editor-foreground);
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background-color: var(--vscode-panel-background);
            border-radius: 8px;
            border: 1px solid var(--vscode-panel-border);
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-card {
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 15px;
        }
        
        .status-title {
            font-weight: bold;
            margin-bottom: 10px;
            color: var(--vscode-textLink-foreground);
        }
        
        .status-value {
            font-size: 1.2em;
            margin-bottom: 5px;
        }
        
        .status-description {
            font-size: 0.9em;
            color: var(--vscode-descriptionForeground);
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background-color: var(--vscode-progressBar-background);
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }
        
        .progress-fill {
            height: 100%;
            background-color: var(--vscode-progressBar-background);
            transition: width 0.3s ease;
        }
        
        .progress-fill.good { background-color: #4CAF50; }
        .progress-fill.medium { background-color: #FF9800; }
        .progress-fill.poor { background-color: #F44336; }
        
        .controls {
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            margin-top: 15px;
        }
        
        .btn {
            padding: 8px 16px;
            border: 1px solid var(--vscode-button-border);
            border-radius: 4px;
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.2s;
        }
        
        .btn:hover {
            background-color: var(--vscode-button-hoverBackground);
        }
        
        .btn.primary {
            background-color: var(--vscode-button-background);
        }
        
        .btn.stop { background-color: #F44336; }
        .btn.confirm { background-color: #FF9800; }
        .btn.continue { background-color: #4CAF50; }
        
        .log-section {
            background-color: var(--vscode-panel-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 8px;
            padding: 15px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .log-entry {
            margin-bottom: 8px;
            padding: 5px;
            border-left: 3px solid var(--vscode-textLink-foreground);
            padding-left: 10px;
            font-family: monospace;
            font-size: 0.9em;
        }
        
        .timestamp {
            color: var(--vscode-descriptionForeground);
            margin-right: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧠 Augment Auto Observer</h1>
        <p>Système d'auto-observation et de relance automatique</p>
        <p><strong>Statut:</strong> <span style="color: ${status.isActive ? '#4CAF50' : '#F44336'}">${status.isActive ? 'ACTIF' : 'INACTIF'}</span></p>
    </div>

    <div class="status-grid">
        <div class="status-card">
            <div class="status-title">📊 Phase de Conversation</div>
            <div class="status-value">${status.phase}</div>
            <div class="status-description">État actuel de l'interaction</div>
        </div>

        <div class="status-card">
            <div class="status-title">😊 Satisfaction Utilisateur</div>
            <div class="status-value">${(status.userSatisfaction * 100).toFixed(0)}%</div>
            <div class="progress-bar">
                <div class="progress-fill ${status.userSatisfaction > 0.7 ? 'good' : status.userSatisfaction > 0.4 ? 'medium' : 'poor'}" 
                     style="width: ${status.userSatisfaction * 100}%"></div>
            </div>
            <div class="status-description">Niveau de satisfaction détecté</div>
        </div>

        <div class="status-card">
            <div class="status-title">🤖 Performance IA</div>
            <div class="status-value">${(status.aiPerformance * 100).toFixed(0)}%</div>
            <div class="progress-bar">
                <div class="progress-fill ${status.aiPerformance > 0.7 ? 'good' : status.aiPerformance > 0.4 ? 'medium' : 'poor'}" 
                     style="width: ${status.aiPerformance * 100}%"></div>
            </div>
            <div class="status-description">Qualité des réponses IA</div>
        </div>

        <div class="status-card">
            <div class="status-title">🎯 Objectifs Détectés</div>
            <div class="status-value">${status.objectivesCount}</div>
            <div class="status-description">Nombre d'objectifs identifiés</div>
        </div>

        <div class="status-card">
            <div class="status-title">⚠️ Corrections Reçues</div>
            <div class="status-value">${status.correctionsCount}</div>
            <div class="status-description">Corrections utilisateur récentes</div>
        </div>

        <div class="status-card">
            <div class="status-title">🔄 Système de Relance</div>
            <div class="status-value">${status.waitingForChoice ? 'EN ATTENTE' : 'SURVEILLANCE'}</div>
            <div class="status-description">
                ${status.waitingForChoice ?
            `Relance dans ${status.timeUntilRelaunch}s` :
            'Surveillance active'}
            </div>
        </div>
    </div>

    <div class="controls">
        <h3>🎯 Validation à 3 Issues</h3>
        <p>Contrôlez le comportement de l'IA :</p>
        <div class="button-group">
            <button class="btn stop" onclick="sendMessage('choice1')">1. 🛑 STOP - Arrêter pour clarification</button>
            <button class="btn confirm" onclick="sendMessage('choice2')">2. ✅ CONFIRM - Confirmer compréhension mutuelle</button>
            <button class="btn continue" onclick="sendMessage('choice3')">3. 🚀 CONTINUE - Continuer avec confiance</button>
        </div>
        
        <h3>🔧 Actions</h3>
        <div class="button-group">
            <button class="btn primary" onclick="sendMessage('forceRelaunch')">🔄 Forcer Relance</button>
            <button class="btn" onclick="sendMessage('refresh')">🔄 Actualiser</button>
        </div>
    </div>

    <div class="log-section">
        <h3>📝 Journal d'Activité</h3>
        <div class="log-entry">
            <span class="timestamp">${new Date().toLocaleTimeString()}</span>
            Système d'auto-observation actif
        </div>
        <div class="log-entry">
            <span class="timestamp">${new Date().toLocaleTimeString()}</span>
            Phase: ${status.phase} | Satisfaction: ${(status.userSatisfaction * 100).toFixed(0)}%
        </div>
        <div class="log-entry">
            <span class="timestamp">${new Date().toLocaleTimeString()}</span>
            Recommandation: ${status.recommendedAction.toUpperCase()}
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();
        
        function sendMessage(command) {
            vscode.postMessage({ command: command });
        }
        
        // Auto-refresh toutes les 5 secondes
        setInterval(() => {
            sendMessage('refresh');
        }, 5000);
    </script>
</body>
</html>`;
    }
    getStatusData() {
        // Simuler des données de statut (à remplacer par de vraies données)
        return {
            isActive: true,
            phase: 'collaboration',
            userSatisfaction: 0.75,
            aiPerformance: 0.82,
            objectivesCount: 3,
            correctionsCount: 1,
            waitingForChoice: false,
            timeUntilRelaunch: 25,
            recommendedAction: 'continue'
        };
    }
}
exports.StatusPanel = StatusPanel;
//# sourceMappingURL=statusPanel.js.map