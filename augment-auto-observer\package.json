{"name": "augment-auto-observer", "displayName": "Augment Auto Observer", "description": "Extension pour auto-observation permanente des conversations Augment avec système de relance automatique", "version": "1.0.0", "engines": {"vscode": "^1.74.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augment-auto-observer.start", "title": "Démarrer Auto-Observation", "category": "Augment Observer"}, {"command": "augment-auto-observer.stop", "title": "Arrêter Auto-Observation", "category": "Augment Observer"}, {"command": "augment-auto-observer.showStatus", "title": "Afficher Statut Conscience", "category": "Augment Observer"}, {"command": "augment-auto-observer.forceRelaunch", "title": "Forcer Relance IA", "category": "Augment Observer"}, {"command": "augment-auto-observer.choice1", "title": "1. STOP - Arrêter IA", "category": "Augment Observer"}, {"command": "augment-auto-observer.choice2", "title": "2. <PERSON><PERSON><PERSON><PERSON> - Con<PERSON>rmer", "category": "Augment Observer"}, {"command": "augment-auto-observer.choice3", "title": "3. CONTINUE - <PERSON><PERSON><PERSON>", "category": "Augment Observer"}], "views": {"explorer": [{"id": "augmentObserverStatus", "name": "Augment Auto Observer", "when": "augment-auto-observer.active"}]}, "viewsContainers": {"activitybar": [{"id": "augment-observer", "title": "Augment Observer", "icon": "$(eye)"}]}, "configuration": {"title": "Augment Auto Observer", "properties": {"augment-auto-observer.inactivityTimeout": {"type": "number", "default": 30, "description": "<PERSON><PERSON>lai d'inactivité en secondes avant relance automatique"}, "augment-auto-observer.relaunchCooldown": {"type": "number", "default": 60, "description": "<PERSON><PERSON><PERSON> entre relances en secondes"}, "augment-auto-observer.enableNotifications": {"type": "boolean", "default": true, "description": "Activer les notifications visuelles"}}}}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"sqlite3": "^5.1.6", "chokidar": "^3.5.3"}}