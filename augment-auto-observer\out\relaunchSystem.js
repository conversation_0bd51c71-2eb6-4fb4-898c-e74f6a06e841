"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.RelaunchSystem = void 0;
const vscode = require("vscode");
class RelaunchSystem {
    constructor(context) {
        this.isActive = false;
        this.waitingForUserChoice = false;
        this.stopRequested = false;
        this.inactivityTimeout = 30; // 30 secondes
        this.relaunchCooldown = 60; // 1 minute
        this.context = context;
        // Lire la configuration
        const config = vscode.workspace.getConfiguration('augment-auto-observer');
        this.inactivityTimeout = config.get('inactivityTimeout', 30);
        this.relaunchCooldown = config.get('relaunchCooldown', 60);
    }
    async start() {
        if (this.isActive) {
            return;
        }
        this.isActive = true;
        this.startMonitoring();
        console.log('🔄 RelaunchSystem démarré');
    }
    stop() {
        if (!this.isActive) {
            return;
        }
        this.isActive = false;
        if (this.monitoringTimer) {
            clearInterval(this.monitoringTimer);
            this.monitoringTimer = undefined;
        }
        console.log('🛑 RelaunchSystem arrêté');
    }
    onRelaunchNeeded(callback) {
        this.onRelaunchNeededCallback = callback;
    }
    processMessages(messages) {
        if (!this.isActive || messages.length === 0) {
            return;
        }
        for (const message of messages) {
            // Détecter messages IA avec 3 issues
            if (message.assistantResponse && this.detectThreeWayValidation(message.assistantResponse)) {
                this.waitingForUserChoice = true;
                this.lastAIMessageTime = Date.now();
                this.stopRequested = false;
                console.log('🎯 Message IA avec 3 issues détecté - Surveillance activée');
                // Afficher notification avec boutons
                this.showValidationNotification();
            }
            // Détecter réponses utilisateur
            if (message.userMessage) {
                this.processUserMessage(message.userMessage);
            }
        }
    }
    processUserChoice(choice) {
        this.waitingForUserChoice = false;
        switch (choice) {
            case 1: // STOP
                this.stopRequested = true;
                console.log('🛑 STOP demandé - IA en pause');
                vscode.window.showInformationMessage('🛑 IA arrêtée pour clarification');
                break;
            case 2: // CONFIRM
                this.stopRequested = false;
                console.log('✅ CONFIRM - Validation mutuelle');
                break;
            case 3: // CONTINUE
                this.stopRequested = false;
                console.log('🚀 CONTINUE - Poursuite avec confiance');
                break;
        }
    }
    detectThreeWayValidation(content) {
        const patterns = [
            'votre choix (1, 2 ou 3)',
            '1. 🛑',
            '2. ✅',
            '3. 🚀',
            'validation à 3 issues'
        ];
        const contentLower = content.toLowerCase();
        let patternCount = 0;
        for (const pattern of patterns) {
            if (contentLower.includes(pattern.toLowerCase())) {
                patternCount++;
            }
        }
        return patternCount >= 3;
    }
    processUserMessage(userMessage) {
        if (!this.waitingForUserChoice) {
            return;
        }
        const content = userMessage.trim().toLowerCase();
        // Détecter choix "1" ou "stop"
        if (['1', '1.', 'stop', 'arrêt', 'arrêter'].includes(content)) {
            this.processUserChoice(1);
        }
        // Détecter choix "2" ou "3"
        else if (['2', '2.', '3', '3.', 'confirm', 'continue'].includes(content)) {
            const choice = ['2', '2.', 'confirm'].includes(content) ? 2 : 3;
            this.processUserChoice(choice);
        }
        // Si autre réponse, réinitialiser le timer
        else {
            this.lastAIMessageTime = Date.now();
            console.log('⏳ Réponse utilisateur non reconnue, timer réinitialisé');
        }
    }
    startMonitoring() {
        // Vérifier toutes les 5 secondes
        this.monitoringTimer = setInterval(() => {
            this.checkForRelaunch();
        }, 5000);
    }
    checkForRelaunch() {
        if (!this.isActive || !this.waitingForUserChoice || !this.lastAIMessageTime) {
            return;
        }
        const currentTime = Date.now();
        const timeSinceLastAI = (currentTime - this.lastAIMessageTime) / 1000;
        // Si arrêt demandé, attendre plus longtemps (5 minutes)
        if (this.stopRequested) {
            if (timeSinceLastAI >= 300) { // 5 minutes
                console.log('🔄 Réactivation automatique après STOP');
                this.stopRequested = false;
                this.waitingForUserChoice = false;
            }
            return;
        }
        // Vérifier timeout d'inactivité
        if (timeSinceLastAI >= this.inactivityTimeout) {
            // Vérifier cooldown
            if (this.lastRelaunchTime) {
                const timeSinceLastRelaunch = (currentTime - this.lastRelaunchTime) / 1000;
                if (timeSinceLastRelaunch < this.relaunchCooldown) {
                    console.log(`⏳ Relance en cooldown (${Math.round(this.relaunchCooldown - timeSinceLastRelaunch)}s restantes)`);
                    return;
                }
            }
            console.log(`🔍 IA inactive depuis ${Math.round(timeSinceLastAI)}s - Déclenchement relance`);
            this.triggerRelaunch();
        }
    }
    triggerRelaunch() {
        this.lastRelaunchTime = Date.now();
        this.waitingForUserChoice = false;
        console.log('🔄 Déclenchement relance automatique');
        // Notifier le système principal
        if (this.onRelaunchNeededCallback) {
            this.onRelaunchNeededCallback();
        }
        // Afficher notification
        vscode.window.showInformationMessage('🔄 Relance automatique - IA inactive détectée', 'Voir Statut').then(selection => {
            if (selection === 'Voir Statut') {
                vscode.commands.executeCommand('augment-auto-observer.showStatus');
            }
        });
    }
    showValidationNotification() {
        const config = vscode.workspace.getConfiguration('augment-auto-observer');
        const enableNotifications = config.get('enableNotifications', true);
        if (!enableNotifications) {
            return;
        }
        vscode.window.showInformationMessage('🎯 IA propose 3 issues - Votre choix ?', '1. 🛑 STOP', '2. ✅ CONFIRM', '3. 🚀 CONTINUE').then(selection => {
            switch (selection) {
                case '1. 🛑 STOP':
                    vscode.commands.executeCommand('augment-auto-observer.choice1');
                    break;
                case '2. ✅ CONFIRM':
                    vscode.commands.executeCommand('augment-auto-observer.choice2');
                    break;
                case '3. 🚀 CONTINUE':
                    vscode.commands.executeCommand('augment-auto-observer.choice3');
                    break;
            }
        });
    }
    // Méthodes publiques pour l'interface
    isWaitingForChoice() {
        return this.waitingForUserChoice;
    }
    isStopRequested() {
        return this.stopRequested;
    }
    getInactivityDuration() {
        if (!this.lastAIMessageTime) {
            return 0;
        }
        return (Date.now() - this.lastAIMessageTime) / 1000;
    }
    getTimeUntilRelaunch() {
        if (!this.waitingForUserChoice || !this.lastAIMessageTime) {
            return 0;
        }
        const timeSinceLastAI = this.getInactivityDuration();
        return Math.max(0, this.inactivityTimeout - timeSinceLastAI);
    }
    resetStopRequest() {
        this.stopRequested = false;
        this.waitingForUserChoice = false;
        console.log('🔄 Système de relance réactivé manuellement');
    }
}
exports.RelaunchSystem = RelaunchSystem;
//# sourceMappingURL=relaunchSystem.js.map