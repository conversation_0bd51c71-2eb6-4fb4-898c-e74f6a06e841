{"name": "augment-auto-observer-simple", "displayName": "Augment Auto Observer Simple", "description": "Version simplifiée pour auto-observation des conversations Augment", "version": "1.0.0", "publisher": "augment-team", "engines": {"vscode": "^1.74.0"}, "repository": {"type": "git", "url": "https://github.com/user/augment-auto-observer-simple.git"}, "categories": ["Other"], "activationEvents": ["onStartupFinished"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "augment-auto-observer-simple.start", "title": "Démarrer Auto-Observation Simple", "category": "Augment Observer"}, {"command": "augment-auto-observer-simple.stop", "title": "Arrêter Auto-Observation", "category": "Augment Observer"}, {"command": "augment-auto-observer-simple.choice1", "title": "1. STOP - Arrêter IA", "category": "Augment Observer"}, {"command": "augment-auto-observer-simple.choice2", "title": "2. <PERSON><PERSON><PERSON><PERSON> - Con<PERSON>rmer", "category": "Augment Observer"}, {"command": "augment-auto-observer-simple.choice3", "title": "3. CONTINUE - <PERSON><PERSON><PERSON>", "category": "Augment Observer"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"chokidar": "^3.5.3"}}