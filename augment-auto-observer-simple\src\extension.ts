import * as vscode from 'vscode';
import * as fs from 'fs';
import * as path from 'path';
import * as chokidar from 'chokidar';

export function activate(context: vscode.ExtensionContext) {
    console.log('🚀 Augment Auto Observer Simple - Extension activée');

    let isActive = false;
    let statusBarItem: vscode.StatusBarItem;
    let fileWatcher: chokidar.FSWatcher | undefined;
    let lastFileSize = 0;
    let waitingForChoice = false;
    let lastAIMessageTime = 0;
    let relaunchTimer: NodeJS.Timeout | undefined;

    // Créer la barre de statut
    statusBarItem = vscode.window.createStatusBarItem(vscode.StatusBarAlignment.Right, 100);
    context.subscriptions.push(statusBarItem);

    // Commande: Démarrer
    const startCommand = vscode.commands.registerCommand('augment-auto-observer-simple.start', async () => {
        if (isActive) {
            vscode.window.showInformationMessage('Auto-observation déjà active');
            return;
        }

        try {
            const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
            if (!workspaceFolder) {
                throw new Error('Aucun workspace ouvert');
            }

            const logFile = path.join(workspaceFolder.uri.fsPath, 'augment_conversation_auto.txt');
            
            // Vérifier si le fichier existe
            if (!fs.existsSync(logFile)) {
                vscode.window.showErrorMessage('Fichier augment_conversation_auto.txt non trouvé. Démarrez d\'abord le script Python.');
                return;
            }

            // Obtenir la taille initiale
            const stats = fs.statSync(logFile);
            lastFileSize = stats.size;

            // Démarrer la surveillance
            fileWatcher = chokidar.watch(logFile, {
                persistent: true,
                ignoreInitial: true
            });

            fileWatcher.on('change', () => {
                checkForNewContent(logFile);
            });

            isActive = true;
            statusBarItem.text = '$(eye) Auto-Observer: ACTIF';
            statusBarItem.backgroundColor = new vscode.ThemeColor('statusBarItem.prominentBackground');
            statusBarItem.show();

            vscode.window.showInformationMessage('🧠 Auto-observation Simple démarrée !');
            console.log('📊 Surveillance du fichier:', logFile);

        } catch (error) {
            vscode.window.showErrorMessage(`Erreur démarrage: ${error}`);
        }
    });

    // Commande: Arrêter
    const stopCommand = vscode.commands.registerCommand('augment-auto-observer-simple.stop', () => {
        if (!isActive) {
            vscode.window.showInformationMessage('Auto-observation déjà arrêtée');
            return;
        }

        if (fileWatcher) {
            fileWatcher.close();
            fileWatcher = undefined;
        }

        if (relaunchTimer) {
            clearTimeout(relaunchTimer);
            relaunchTimer = undefined;
        }

        isActive = false;
        waitingForChoice = false;
        statusBarItem.text = '$(eye-closed) Auto-Observer: ARRÊTÉ';
        statusBarItem.backgroundColor = undefined;

        vscode.window.showInformationMessage('🛑 Auto-observation arrêtée');
    });

    // Commandes pour les 3 issues
    const choice1Command = vscode.commands.registerCommand('augment-auto-observer-simple.choice1', () => {
        processUserChoice(1);
    });

    const choice2Command = vscode.commands.registerCommand('augment-auto-observer-simple.choice2', () => {
        processUserChoice(2);
    });

    const choice3Command = vscode.commands.registerCommand('augment-auto-observer-simple.choice3', () => {
        processUserChoice(3);
    });

    function checkForNewContent(logFile: string) {
        try {
            const stats = fs.statSync(logFile);
            const currentSize = stats.size;

            if (currentSize > lastFileSize) {
                console.log(`📊 Nouveau contenu détecté: +${currentSize - lastFileSize} bytes`);
                
                // Lire le nouveau contenu
                const content = fs.readFileSync(logFile, 'utf8');
                const newContent = content.slice(lastFileSize);
                
                // Analyser le nouveau contenu
                analyzeNewContent(newContent);
                
                lastFileSize = currentSize;
            }
        } catch (error) {
            console.error('Erreur lecture fichier:', error);
        }
    }

    function analyzeNewContent(content: string) {
        console.log('🔍 Analyse du nouveau contenu...');
        
        // Détecter les messages IA avec 3 issues
        if (detectThreeWayValidation(content)) {
            console.log('🎯 Message IA avec 3 issues détecté !');
            
            waitingForChoice = true;
            lastAIMessageTime = Date.now();
            
            // Afficher notification
            vscode.window.showInformationMessage(
                '🎯 IA propose 3 issues - Votre choix ?',
                '1. 🛑 STOP',
                '2. ✅ CONFIRM',
                '3. 🚀 CONTINUE'
            ).then(selection => {
                switch (selection) {
                    case '1. 🛑 STOP':
                        vscode.commands.executeCommand('augment-auto-observer-simple.choice1');
                        break;
                    case '2. ✅ CONFIRM':
                        vscode.commands.executeCommand('augment-auto-observer-simple.choice2');
                        break;
                    case '3. 🚀 CONTINUE':
                        vscode.commands.executeCommand('augment-auto-observer-simple.choice3');
                        break;
                }
            });

            // Démarrer le timer de relance (30 secondes)
            startRelaunchTimer();
        }
    }

    function detectThreeWayValidation(content: string): boolean {
        const patterns = [
            'votre choix (1, 2 ou 3)',
            '1. 🛑',
            '2. ✅',
            '3. 🚀',
            'validation à 3 issues'
        ];

        const contentLower = content.toLowerCase();
        let patternCount = 0;

        for (const pattern of patterns) {
            if (contentLower.includes(pattern.toLowerCase())) {
                patternCount++;
            }
        }

        return patternCount >= 3;
    }

    function startRelaunchTimer() {
        // Annuler le timer précédent s'il existe
        if (relaunchTimer) {
            clearTimeout(relaunchTimer);
        }

        console.log('⏰ Timer de relance démarré (30 secondes)');

        relaunchTimer = setTimeout(() => {
            if (waitingForChoice) {
                console.log('🔄 Déclenchement relance automatique');
                triggerRelaunch();
            }
        }, 30000); // 30 secondes
    }

    function processUserChoice(choice: number) {
        waitingForChoice = false;
        
        if (relaunchTimer) {
            clearTimeout(relaunchTimer);
            relaunchTimer = undefined;
        }

        switch (choice) {
            case 1: // STOP
                console.log('🛑 STOP demandé - IA en pause');
                vscode.window.showInformationMessage('🛑 IA arrêtée pour clarification');
                break;
            case 2: // CONFIRM
                console.log('✅ CONFIRM - Validation mutuelle');
                vscode.window.showInformationMessage('✅ Validation de la compréhension mutuelle');
                break;
            case 3: // CONTINUE
                console.log('🚀 CONTINUE - Poursuite avec confiance');
                vscode.window.showInformationMessage('🚀 Poursuite avec confiance');
                break;
        }
    }

    function triggerRelaunch() {
        waitingForChoice = false;
        
        console.log('🔄 Tentative de relance IA...');
        
        // Afficher notification de relance
        vscode.window.showInformationMessage(
            '🔄 Relance automatique - IA inactive détectée',
            'Continuer'
        ).then(selection => {
            if (selection === 'Continuer') {
                // Simuler une interaction pour relancer l'IA
                vscode.window.showInformationMessage('🧠 Continue ton auto-observation et analyse notre conversation.');
            }
        });

        // Essayer d'ouvrir le chat Augment
        try {
            vscode.commands.executeCommand('augment.newChat');
        } catch (error) {
            console.error('Erreur ouverture chat Augment:', error);
        }
    }

    // Enregistrer toutes les commandes
    context.subscriptions.push(
        startCommand,
        stopCommand,
        choice1Command,
        choice2Command,
        choice3Command
    );

    // Démarrage automatique
    vscode.commands.executeCommand('augment-auto-observer-simple.start');

    console.log('✅ Augment Auto Observer Simple - Extension prête');
}

export function deactivate() {
    console.log('🛑 Augment Auto Observer Simple - Extension désactivée');
}
