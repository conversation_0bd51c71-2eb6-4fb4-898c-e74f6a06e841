# AUGMENT AUTO RELAUNCH - VERSION SIMPLE
# Script PowerShell pour relancer l'IA automatiquement

param(
    [int]$TimeoutSeconds = 30,
    [string]$LogFile = "augment_conversation_auto.txt",
    [string]$Message = "Continue ton auto-observation"
)

Write-Host "AUGMENT AUTO RELAUNCH - DEMARRAGE" -ForegroundColor Green
Write-Host "Fichier surveille: $LogFile" -ForegroundColor Cyan
Write-Host "Timeout: $TimeoutSeconds secondes" -ForegroundColor Cyan
Write-Host "Message: $Message" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Yellow

# Verifier que le fichier existe
if (-not (Test-Path $LogFile)) {
    Write-Host "ERREUR: Fichier non trouve: $LogFile" -ForegroundColor Red
    exit 1
}

# Variables globales
$LastFileSize = (Get-Item $LogFile).Length
$LastAITime = Get-Date
$IsRunning = $true

# Fonction pour detecter nouveaux messages IA
function Test-NewAIMessage {
    $currentSize = (Get-Item $LogFile).Length
    
    if ($currentSize -gt $script:LastFileSize) {
        $newContent = Get-Content $LogFile -Tail 20 | Out-String
        
        if ($newContent -match "ASSISTANT:") {
            $script:LastAITime = Get-Date
            $script:LastFileSize = $currentSize
            Write-Host "Nouveau message IA detecte" -ForegroundColor Green
            return $true
        }
        
        $script:LastFileSize = $currentSize
    }
    
    return $false
}

# Fonction pour envoyer le message
function Send-AutoMessage {
    try {
        Write-Host "Envoi du message automatique..." -ForegroundColor Yellow
        
        # Copier dans le presse-papier
        Set-Clipboard -Value $Message
        
        # Utiliser automation Windows
        Add-Type -AssemblyName System.Windows.Forms
        
        # Simuler Alt+Tab pour activer VSCode
        [System.Windows.Forms.SendKeys]::SendWait("%{TAB}")
        Start-Sleep -Milliseconds 1000
        
        # Coller le message
        [System.Windows.Forms.SendKeys]::SendWait("^v")
        Start-Sleep -Milliseconds 500
        
        # Envoyer avec Entree
        [System.Windows.Forms.SendKeys]::SendWait("{ENTER}")
        
        Write-Host "Message envoye avec succes!" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "Erreur envoi: $_" -ForegroundColor Red
        
        # Fallback: notification
        Write-Host "RELANCE NECESSAIRE - Collez manuellement: $Message" -ForegroundColor Yellow
        return $false
    }
}

# Boucle principale
Write-Host "Surveillance active..." -ForegroundColor Green

while ($IsRunning) {
    try {
        # Verifier nouveaux messages
        Test-NewAIMessage | Out-Null
        
        # Calculer inactivite
        $inactivity = (Get-Date) - $LastAITime
        $seconds = [math]::Round($inactivity.TotalSeconds)
        
        # Afficher statut
        Write-Host "Inactivite: $seconds / $TimeoutSeconds secondes" -ForegroundColor Gray
        
        # Verifier si relance necessaire
        if ($seconds -ge $TimeoutSeconds) {
            Write-Host "INACTIVITE DETECTEE - RELANCE!" -ForegroundColor Red
            
            if (Send-AutoMessage) {
                # Reinitialiser le timer
                $LastAITime = Get-Date
                Start-Sleep -Seconds 10
            } else {
                Start-Sleep -Seconds 30
            }
        }
        
        Start-Sleep -Seconds 2
    }
    catch {
        Write-Host "Erreur: $_" -ForegroundColor Red
        Start-Sleep -Seconds 5
    }
}

Write-Host "Script termine" -ForegroundColor Yellow
