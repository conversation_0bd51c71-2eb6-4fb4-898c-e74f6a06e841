#!/usr/bin/env python3
"""
ANALYSEUR TEMPOREL AUGMENT - MESURE PRÉCISE DES DÉLAIS
Analyse le temps d'enregistrement des messages :
- Messages utilisateur : Interface → DB
- Messages assistant : Génération → DB
- Métriques détaillées avec timestamps précis
"""

import os
import time
import threading
from datetime import datetime
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler
import json
import sqlite3

class AnalyseurTemporelAugment(FileSystemEventHandler):
    def __init__(self):
        self.workspace_path = r"C:\Users\<USER>\AppData\Roaming\Code\User\workspaceStorage\a35ba43ef26792e61fe787a234121806"
        self.db_path = os.path.join(self.workspace_path, "state.vscdb")
        self.wal_path = self.db_path + "-wal"
        self.output_file = "analyse_temporelle_augment.txt"
        
        self.running = True
        self.observer = None
        self.lock = threading.Lock()
        self.dernier_nombre_messages = 0
        self.numero_analyse = 1
        
        # TIMINGS AUGMENT
        self.AUGMENT_TIMEOUT_MS = 800
        self.AUGMENT_MAX_WAIT_MS = 1600
        
        # ANALYSE TEMPORELLE
        self.historique_detections = []
        self.derniere_taille_wal = 0
        self.sequence_detection = 0
        
        self.init_output_file()
        self.init_wal_baseline()
        self.charger_etat_initial()
    
    def init_output_file(self):
        """Initialise le fichier d'analyse temporelle"""
        with open(self.output_file, 'w', encoding='utf-8') as f:
            f.write("ANALYSEUR TEMPOREL AUGMENT - DÉLAIS D'ENREGISTREMENT\n")
            f.write(f"Démarré: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n")
            f.write("🎯 OBJECTIF : Mesurer les délais Interface → DB\n")
            f.write("📊 MÉTRIQUES ANALYSÉES :\n")
            f.write("   • Timestamp détection WAL\n")
            f.write("   • Délai jusqu'à extraction réussie\n")
            f.write("   • Taille changement WAL\n")
            f.write("   • Type de message (user/assistant)\n")
            f.write("   • Contenu du message\n")
            f.write("   • Délai total estimé\n")
            f.write(f"⏱️  TIMINGS AUGMENT : {self.AUGMENT_TIMEOUT_MS}ms → {self.AUGMENT_MAX_WAIT_MS}ms\n")
            f.write("=" * 80 + "\n\n")
    
    def init_wal_baseline(self):
        """Établit la baseline de la taille WAL"""
        try:
            if os.path.exists(self.wal_path):
                self.derniere_taille_wal = os.path.getsize(self.wal_path)
                print(f"📊 WAL baseline: {self.derniere_taille_wal} bytes")
            else:
                print("⚠️ Fichier WAL non trouvé")
                self.derniere_taille_wal = 0
        except Exception as e:
            print(f"❌ Erreur WAL baseline: {e}")
            self.derniere_taille_wal = 0
    
    def charger_etat_initial(self):
        """Charge l'état initial pour comparaison"""
        try:
            messages_existants = self.extraire_messages()
            self.dernier_nombre_messages = len(messages_existants)
            
            print(f"📊 Messages baseline: {self.dernier_nombre_messages}")
            print(f"🔢 Prochaine analyse: #{self.numero_analyse}")
            
        except Exception as e:
            print(f"❌ Erreur baseline messages: {e}")
            self.dernier_nombre_messages = 0
    
    def extraire_messages(self):
        """Extraction simple des messages pour comptage"""
        try:
            conn = sqlite3.connect(self.db_path, timeout=1.0)
            conn.execute("PRAGMA journal_mode=WAL")
            cursor = conn.cursor()
            
            cursor.execute("SELECT key, value FROM ItemTable WHERE key LIKE '%augment-chat%'")
            rows = cursor.fetchall()
            
            for key, value in rows:
                if 'webviewView.augment-chat' in key:
                    try:
                        if isinstance(value, bytes):
                            value_str = value.decode('utf-8')
                        else:
                            value_str = str(value)
                        
                        main_data = json.loads(value_str)
                        webview_data = json.loads(main_data['webviewState'])
                        
                        conversations = webview_data.get('conversations', {})
                        current_conv_id = webview_data.get('currentConversationId')
                        
                        if current_conv_id and current_conv_id in conversations:
                            chat_history = conversations[current_conv_id].get('chatHistory', [])
                            conn.close()
                            return chat_history
                            
                    except Exception as e:
                        continue
            
            conn.close()
            return []
            
        except Exception as e:
            return []
    
    def on_modified(self, event):
        """Détection WAL avec analyse temporelle complète"""
        if event.is_directory:
            return
        
        if os.path.basename(event.src_path) == "state.vscdb-wal":
            timestamp_detection = datetime.now()
            timestamp_str = timestamp_detection.strftime('%H:%M:%S.%f')[:-3]
            
            # Analyser le changement de taille WAL
            try:
                nouvelle_taille_wal = os.path.getsize(self.wal_path)
                delta_wal = nouvelle_taille_wal - self.derniere_taille_wal
                self.derniere_taille_wal = nouvelle_taille_wal
            except:
                delta_wal = 0
            
            self.sequence_detection += 1
            
            print(f"⚡ [{timestamp_str}] WAL #{self.sequence_detection} - ANALYSE TEMPORELLE...")
            print(f"   📊 Delta WAL: +{delta_wal} bytes")
            
            # Enregistrer la détection
            detection = {
                'sequence': self.sequence_detection,
                'timestamp': timestamp_detection,
                'delta_wal': delta_wal,
                'messages_avant': self.dernier_nombre_messages
            }
            self.historique_detections.append(detection)
            
            # Lancer l'analyse temporelle
            threading.Thread(target=self.analyser_delais_enregistrement, args=(detection,)).start()
    
    def analyser_delais_enregistrement(self, detection):
        """Analyse complète des délais d'enregistrement"""
        sequence = detection['sequence']
        timestamp_detection = detection['timestamp']
        delta_wal = detection['delta_wal']
        messages_avant = detection['messages_avant']
        
        # Série de mesures temporelles
        mesures = []
        
        # Mesures à intervalles précis
        intervalles_ms = [100, 200, 400, 600, 800, 1000, 1200, 1400, 1600, 1800, 2000]
        
        for intervalle in intervalles_ms:
            time.sleep(intervalle / 1000.0)
            
            timestamp_mesure = datetime.now()
            delai_ms = (timestamp_mesure - timestamp_detection).total_seconds() * 1000
            
            # Extraire les messages
            messages_actuels = self.extraire_messages()
            nombre_actuel = len(messages_actuels)
            
            mesure = {
                'delai_ms': delai_ms,
                'nombre_messages': nombre_actuel,
                'nouveaux_messages': nombre_actuel - messages_avant,
                'timestamp': timestamp_mesure
            }
            mesures.append(mesure)
            
            print(f"   📊 {delai_ms:4.0f}ms: {messages_avant} → {nombre_actuel} messages (+{mesure['nouveaux_messages']})")
            
            # Si on détecte de nouveaux messages, analyser en détail
            if mesure['nouveaux_messages'] > 0:
                self.analyser_nouveaux_messages(detection, mesure, messages_actuels)
                break
        
        # Si aucun nouveau message détecté
        if all(m['nouveaux_messages'] == 0 for m in mesures):
            print(f"   ⚠️ Aucun nouveau message détecté après 2000ms")
            self.enregistrer_analyse_echec(detection, mesures)
    
    def analyser_nouveaux_messages(self, detection, mesure_succes, messages_actuels):
        """Analyse détaillée des nouveaux messages détectés"""
        sequence = detection['sequence']
        delai_detection = mesure_succes['delai_ms']
        nouveaux_count = mesure_succes['nouveaux_messages']
        messages_avant = detection['messages_avant']
        
        print(f"🎯 SUCCÈS #{sequence} - {nouveaux_count} messages après {delai_detection:.0f}ms")
        
        # Extraire les nouveaux messages
        nouveaux_messages = messages_actuels[messages_avant:]
        
        # Analyser chaque nouveau message
        for i, message in enumerate(nouveaux_messages):
            role = message.get('role', 'unknown')
            content = message.get('content', '')
            message_id = message.get('id', 'no-id')
            
            # Déterminer le type de message
            if role == 'user':
                type_message = '👤 UTILISATEUR'
                delai_estime = delai_detection  # Délai direct
            elif role == 'assistant':
                type_message = '🤖 ASSISTANT'
                delai_estime = delai_detection  # Délai de génération + écriture
            else:
                type_message = f'❓ {role.upper()}'
                delai_estime = delai_detection
            
            # Analyser le contenu
            if content and content.strip():
                longueur_contenu = len(content)
                apercu_contenu = content[:100].replace('\n', ' ')
                statut_contenu = "COMPLET"
            else:
                longueur_contenu = 0
                apercu_contenu = "[vide]"
                statut_contenu = "VIDE"
            
            print(f"   ✅ MESSAGE #{self.numero_analyse} - {type_message}")
            print(f"      • Délai: {delai_estime:.0f}ms")
            print(f"      • Contenu: {statut_contenu} ({longueur_contenu} chars)")
            print(f"      • Aperçu: {apercu_contenu}")
            print(f"      • ID: {message_id}")
            
            # Enregistrer l'analyse
            self.enregistrer_analyse_succes(detection, mesure_succes, message, i)
            self.numero_analyse += 1
        
        # Mettre à jour le compteur
        self.dernier_nombre_messages = len(messages_actuels)
    
    def enregistrer_analyse_succes(self, detection, mesure, message, index):
        """Enregistre une analyse réussie"""
        with self.lock:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    timestamp_str = detection['timestamp'].strftime('%H:%M:%S.%f')[:-3]
                    
                    f.write(f"[{timestamp_str}] ANALYSE #{self.numero_analyse} - SUCCÈS\n")
                    f.write(f"Séquence WAL: #{detection['sequence']} | Delta: +{detection['delta_wal']} bytes\n")
                    f.write(f"Délai détection → extraction: {mesure['delai_ms']:.0f}ms\n")
                    f.write(f"Type: {message.get('role', 'unknown')} | Index: {index}\n")
                    f.write("-" * 60 + "\n")
                    
                    content = message.get('content', '')
                    if content and content.strip():
                        content_clean = content[:500]  # Limiter à 500 chars
                        f.write(f"{content_clean}\n")
                        if len(content) > 500:
                            f.write("[... contenu tronqué ...]\n")
                    else:
                        f.write("[Message vide ou en cours de génération]\n")
                    
                    f.write("=" * 80 + "\n\n")
                    f.flush()
                    
            except Exception as e:
                print(f"   ❌ Erreur enregistrement: {e}")
    
    def enregistrer_analyse_echec(self, detection, mesures):
        """Enregistre une analyse échouée"""
        with self.lock:
            try:
                with open(self.output_file, 'a', encoding='utf-8') as f:
                    timestamp_str = detection['timestamp'].strftime('%H:%M:%S.%f')[:-3]
                    
                    f.write(f"[{timestamp_str}] ANALYSE #{self.numero_analyse} - ÉCHEC\n")
                    f.write(f"Séquence WAL: #{detection['sequence']} | Delta: +{detection['delta_wal']} bytes\n")
                    f.write("Aucun nouveau message détecté après 2000ms\n")
                    f.write("Mesures temporelles :\n")
                    
                    for mesure in mesures:
                        f.write(f"  {mesure['delai_ms']:4.0f}ms: {mesure['nombre_messages']} messages\n")
                    
                    f.write("=" * 80 + "\n\n")
                    f.flush()
                    
            except Exception as e:
                print(f"   ❌ Erreur enregistrement échec: {e}")
    
    def demarrer_surveillance(self):
        """Démarre l'analyseur temporel"""
        print("🚀 ANALYSEUR TEMPOREL AUGMENT DÉMARRÉ")
        print("=" * 60)
        print(f"📁 Workspace: a35ba43ef26792e6...")
        print(f"📄 Analyse: {self.output_file}")
        print(f"🔢 Messages baseline: {self.dernier_nombre_messages}")
        print(f"📊 WAL baseline: {self.derniere_taille_wal} bytes")
        print("⏱️  MESURES TEMPORELLES :")
        print("   • Détection WAL instantanée")
        print("   • Mesures à 100, 200, 400, 600, 800ms...")
        print("   • Jusqu'à 2000ms maximum")
        print("   • Analyse détaillée des nouveaux messages")
        print("💬 Écrivez dans Augment pour analyser les délais...")
        print("⏹️  Ctrl+C pour arrêter")
        print("=" * 60)
        
        # Surveillance filesystem
        self.observer = Observer()
        self.observer.schedule(self, self.workspace_path, recursive=False)
        self.observer.start()
        
        try:
            while self.running:
                threading.Event().wait(1)
                
        except KeyboardInterrupt:
            print("\n🛑 Arrêt de l'analyseur temporel...")
            self.running = False
            if self.observer:
                self.observer.stop()
                self.observer.join()

if __name__ == "__main__":
    analyseur = AnalyseurTemporelAugment()
    analyseur.demarrer_surveillance()
