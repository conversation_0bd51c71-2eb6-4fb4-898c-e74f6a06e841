#!/usr/bin/env python3
"""
ANALYSE DU TIMING DE GÉNÉRATION DES MESSAGES
============================================

Script pour analyser en détail comment et quand les messages sont générés
depuis l'interface jusqu'au stockage final.
"""

import sqlite3
import json
import time
import datetime
from pathlib import Path
import threading

class MessageTimingAnalyzer:
    def __init__(self):
        self.state_file = Path("C:/Users/<USER>/AppData/Roaming/Code/User/workspaceStorage/a35ba43ef26792e61fe787a234121806/state.vscdb")
        self.last_check_time = time.time()
        self.message_states = {}
        self.running = False
        
        print(f"🔍 ANALYSE DU TIMING DES MESSAGES")
        print(f"=" * 50)
        print(f"📄 Fichier surveillé: {self.state_file}")
        
    def get_current_conversation_state(self):
        """Obtient l'état actuel complet de la conversation"""
        try:
            conn = sqlite3.connect(str(self.state_file))
            cursor = conn.cursor()
            
            cursor.execute("SELECT value FROM ItemTable WHERE key = 'memento/webviewView.augment-chat'")
            result = cursor.fetchone()
            
            if not result:
                conn.close()
                return None
            
            value = result[0]
            if isinstance(value, bytes):
                value_str = value.decode('utf-8')
            else:
                value_str = str(value)
            
            main_data = json.loads(value_str)
            webview_data = json.loads(main_data['webviewState'])
            
            conn.close()
            return webview_data
            
        except Exception as e:
            print(f"❌ Erreur lecture: {e}")
            return None
    
    def analyze_message_structure(self, message_data, index):
        """Analyse la structure détaillée d'un message"""
        analysis = {
            'index': index,
            'timestamp': datetime.datetime.now().isoformat(),
            'has_request': 'request_message' in message_data,
            'has_response': False,
            'response_complete': False,
            'tools_count': 0,
            'content_length': 0,
            'raw_keys': list(message_data.keys())
        }
        
        # Analyser la réponse
        if 'structured_output_nodes' in message_data:
            nodes = message_data['structured_output_nodes']
            analysis['nodes_count'] = len(nodes)
            
            for node in nodes:
                if node.get('type') == 0 and node.get('content'):
                    analysis['has_response'] = True
                    analysis['content_length'] = len(node['content'])
                    # Vérifier si la réponse semble complète
                    content = node['content']
                    if content.endswith('.') or content.endswith('!') or content.endswith('?'):
                        analysis['response_complete'] = True
                elif node.get('type') == 5:
                    analysis['tools_count'] += 1
        
        return analysis
    
    def detect_message_changes(self):
        """Détecte les changements dans les messages"""
        current_time = time.time()
        conversation_data = self.get_current_conversation_state()
        
        if not conversation_data:
            return
        
        conversations = conversation_data.get('conversations', {})
        current_conv_id = conversation_data.get('currentConversationId')
        
        if not current_conv_id or current_conv_id not in conversations:
            return
        
        chat_history = conversations[current_conv_id].get('chatHistory', [])
        
        print(f"\n[{datetime.datetime.now().strftime('%H:%M:%S.%f')[:-3]}] SCAN")
        print(f"📊 Total messages: {len(chat_history)}")
        
        # Analyser chaque message
        for i, message_data in enumerate(chat_history):
            msg_id = f"msg_{i}"
            analysis = self.analyze_message_structure(message_data, i + 1)
            
            # Comparer avec l'état précédent
            if msg_id in self.message_states:
                prev_analysis = self.message_states[msg_id]
                
                # Détecter les changements
                changes = []
                if prev_analysis['has_response'] != analysis['has_response']:
                    changes.append("réponse apparue")
                if prev_analysis['content_length'] != analysis['content_length']:
                    changes.append(f"contenu: {prev_analysis['content_length']} → {analysis['content_length']} chars")
                if prev_analysis['response_complete'] != analysis['response_complete']:
                    changes.append("réponse complétée")
                if prev_analysis['tools_count'] != analysis['tools_count']:
                    changes.append(f"outils: {prev_analysis['tools_count']} → {analysis['tools_count']}")
                
                if changes:
                    print(f"🔄 Message #{i+1}: {', '.join(changes)}")
            else:
                # Nouveau message
                print(f"✨ Nouveau message #{i+1}:")
                print(f"   📝 Requête: {'✅' if analysis['has_request'] else '❌'}")
                print(f"   🤖 Réponse: {'✅' if analysis['has_response'] else '❌'}")
                print(f"   📏 Taille: {analysis['content_length']} chars")
                print(f"   🔧 Outils: {analysis['tools_count']}")
                print(f"   ✅ Complète: {'✅' if analysis['response_complete'] else '❌'}")
            
            # Sauvegarder l'état actuel
            self.message_states[msg_id] = analysis
    
    def start_realtime_monitoring(self):
        """Démarre la surveillance temps réel"""
        print(f"\n🚀 SURVEILLANCE TEMPS RÉEL DÉMARRÉE")
        print(f"💡 Écrivez un message dans Augment pour voir l'analyse...")
        print(f"⏹️  Ctrl+C pour arrêter")
        
        self.running = True
        
        try:
            while self.running:
                self.detect_message_changes()
                time.sleep(0.05)  # Scan très fréquent (50ms)
                
        except KeyboardInterrupt:
            print(f"\n🛑 Surveillance arrêtée")
            self.running = False
    
    def analyze_specific_message_timing(self):
        """Analyse le timing d'un message spécifique"""
        print(f"\n🎯 ANALYSE SPÉCIFIQUE - ÉCRIVEZ UN MESSAGE MAINTENANT")
        
        # État initial
        initial_data = self.get_current_conversation_state()
        if not initial_data:
            print("❌ Impossible d'obtenir l'état initial")
            return
        
        conversations = initial_data.get('conversations', {})
        current_conv_id = initial_data.get('currentConversationId')
        initial_count = len(conversations[current_conv_id].get('chatHistory', []))
        
        print(f"📊 Messages initiaux: {initial_count}")
        print(f"⏳ En attente d'un nouveau message...")
        
        # Attendre un nouveau message
        while True:
            time.sleep(0.02)  # Scan très rapide
            current_data = self.get_current_conversation_state()
            
            if current_data:
                conversations = current_data.get('conversations', {})
                current_conv_id = current_data.get('currentConversationId')
                current_count = len(conversations[current_conv_id].get('chatHistory', []))
                
                if current_count > initial_count:
                    print(f"🎉 Nouveau message détecté!")
                    
                    # Analyser l'évolution du nouveau message
                    new_message_index = current_count - 1
                    start_time = time.time()
                    
                    print(f"📈 ÉVOLUTION DU MESSAGE #{current_count}:")
                    
                    for scan_count in range(100):  # 100 scans max
                        time.sleep(0.1)
                        
                        scan_data = self.get_current_conversation_state()
                        if scan_data:
                            conversations = scan_data.get('conversations', {})
                            chat_history = conversations[current_conv_id].get('chatHistory', [])
                            
                            if len(chat_history) > new_message_index:
                                message_data = chat_history[new_message_index]
                                analysis = self.analyze_message_structure(message_data, current_count)
                                
                                elapsed = time.time() - start_time
                                print(f"   [{elapsed:.2f}s] Réponse: {'✅' if analysis['has_response'] else '❌'}, "
                                      f"Taille: {analysis['content_length']}, "
                                      f"Complète: {'✅' if analysis['response_complete'] else '❌'}")
                                
                                if analysis['response_complete'] and analysis['content_length'] > 50:
                                    print(f"✅ Message complet détecté après {elapsed:.2f}s")
                                    break
                    
                    break

def main():
    """Fonction principale"""
    analyzer = MessageTimingAnalyzer()
    
    print(f"🎯 CHOISISSEZ LE MODE D'ANALYSE:")
    print(f"1. Surveillance temps réel continue")
    print(f"2. Analyse d'un message spécifique")
    
    choice = input("Votre choix (1 ou 2): ").strip()
    
    if choice == "1":
        analyzer.start_realtime_monitoring()
    elif choice == "2":
        analyzer.analyze_specific_message_timing()
    else:
        print("❌ Choix invalide")

if __name__ == "__main__":
    main()
