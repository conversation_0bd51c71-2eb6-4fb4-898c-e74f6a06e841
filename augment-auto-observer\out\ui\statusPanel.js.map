{"version": 3, "file": "statusPanel.js", "sourceRoot": "", "sources": ["../../src/ui/statusPanel.ts"], "names": [], "mappings": ";;;AAAA,iCAAiC;AAEjC,MAAa,WAAW;IAIpB,YAAY,OAAgC;QACxC,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;IAC3B,CAAC;IAED,IAAI;QACA,IAAI,IAAI,CAAC,KAAK,EAAE;YACZ,IAAI,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC;YACpB,OAAO;SACV;QAED,IAAI,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CACzC,uBAAuB,EACvB,gCAAgC,EAChC,MAAM,CAAC,UAAU,CAAC,MAAM,EACxB;YACI,aAAa,EAAE,IAAI;YACnB,uBAAuB,EAAE,IAAI;SAChC,CACJ,CAAC;QAEF,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;QAEnD,8BAA8B;QAC9B,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,GAAG,EAAE;YACzB,IAAI,CAAC,KAAK,GAAG,SAAS,CAAC;QAC3B,CAAC,CAAC,CAAC;QAEH,gCAAgC;QAChC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,mBAAmB,CAClC,OAAO,CAAC,EAAE;YACN,QAAQ,OAAO,CAAC,OAAO,EAAE;gBACrB,KAAK,SAAS;oBACV,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBAChE,MAAM;gBACV,KAAK,SAAS;oBACV,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBAChE,MAAM;gBACV,KAAK,SAAS;oBACV,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,+BAA+B,CAAC,CAAC;oBAChE,MAAM;gBACV,KAAK,eAAe;oBAChB,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAC,qCAAqC,CAAC,CAAC;oBACtE,MAAM;gBACV,KAAK,SAAS;oBACV,IAAI,CAAC,aAAa,EAAE,CAAC;oBACrB,MAAM;aACb;QACL,CAAC,CACJ,CAAC;QAEF,iDAAiD;QACjD,MAAM,cAAc,GAAG,WAAW,CAAC,GAAG,EAAE;YACpC,IAAI,IAAI,CAAC,KAAK,EAAE;gBACZ,IAAI,CAAC,aAAa,EAAE,CAAC;aACxB;iBAAM;gBACH,aAAa,CAAC,cAAc,CAAC,CAAC;aACjC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;IACb,CAAC;IAEO,aAAa;QACjB,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE;YACb,OAAO;SACV;QAED,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,CAAC;IACvD,CAAC;IAEO,iBAAiB;QACrB,4DAA4D;QAC5D,MAAM,MAAM,GAAG,IAAI,CAAC,aAAa,EAAE,CAAC;QAEpC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DA2I2C,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,SAAS,KAAK,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,SAAS;;;;;;wCAMnG,MAAM,CAAC,KAAK;;;;;;wCAMZ,CAAC,MAAM,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;4CAEtC,MAAM,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;qCACjG,MAAM,CAAC,gBAAgB,GAAG,GAAG;;;;;;;wCAO1B,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;4CAEnC,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,aAAa,GAAG,GAAG,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,CAAC,MAAM;qCAC3F,MAAM,CAAC,aAAa,GAAG,GAAG;;;;;;;wCAOvB,MAAM,CAAC,eAAe;;;;;;wCAMtB,MAAM,CAAC,gBAAgB;;;;;;wCAMvB,MAAM,CAAC,gBAAgB,CAAC,CAAC,CAAC,YAAY,CAAC,CAAC,CAAC,cAAc;;kBAE7E,MAAM,CAAC,gBAAgB,CAAC,CAAC;YACvB,gBAAgB,MAAM,CAAC,iBAAiB,GAAG,CAAC,CAAC;YAC7C,qBACJ;;;;;;;;;;;;;;;;;;;;;;;;sCAwBsB,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;;;;sCAI/B,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;qBAChD,MAAM,CAAC,KAAK,oBAAoB,CAAC,MAAM,CAAC,gBAAgB,GAAG,GAAG,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;;;sCAGzD,IAAI,IAAI,EAAE,CAAC,kBAAkB,EAAE;8BACvC,MAAM,CAAC,iBAAiB,CAAC,WAAW,EAAE;;;;;;;;;;;;;;;;;QAiB5D,CAAC;IACL,CAAC;IAEO,aAAa;QACjB,oEAAoE;QACpE,OAAO;YACH,QAAQ,EAAE,IAAI;YACd,KAAK,EAAE,eAAe;YACtB,gBAAgB,EAAE,IAAI;YACtB,aAAa,EAAE,IAAI;YACnB,eAAe,EAAE,CAAC;YAClB,gBAAgB,EAAE,CAAC;YACnB,gBAAgB,EAAE,KAAK;YACvB,iBAAiB,EAAE,EAAE;YACrB,iBAAiB,EAAE,UAAU;SAChC,CAAC;IACN,CAAC;CACJ;AA3UD,kCA2UC"}